#!/usr/bin/env python3
"""
Create correct ground truth dataset based on model predictions + human annotations.
This script:
1. Takes model's original predictions (f_type + f_sub_type)
2. Applies human review corrections
3. Generates final GT labels by sub-category
"""

import json
import re
from pathlib import Path
from collections import defaultdict

def clean_html_content(content):
    """Extract clean text from HTML/JSON content."""
    if not content:
        return ""
    
    # If it's JSON, try to parse it
    if content.strip().startswith('{'):
        try:
            data = json.loads(content)
            if 'content' in data:
                content = data['content']
        except:
            pass
    
    # Remove HTML tags
    content = re.sub(r'<[^>]+>', ' ', content)
    # Decode HTML entities
    content = content.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
    content = content.replace('&quot;', '"').replace('&#39;', "'").replace('&nbsp;', ' ')
    # Clean up whitespace
    content = re.sub(r'\s+', ' ', content).strip()
    
    return content

def determine_final_ground_truth(annotation):
    """
    Determine the final ground truth label based on model prediction + human review.
    
    Args:
        annotation: Dict with model prediction and human review
        
    Returns:
        Final ground truth category (string)
    """
    model_type = annotation.get('f_type', '')
    model_sub_type = annotation.get('f_sub_type', '')
    human_review = annotation.get('human_review', '')
    correct_result = annotation.get('correct_result', '')
    
    # Combine model prediction
    model_prediction = f"{model_type}-{model_sub_type}" if model_sub_type else model_type
    
    # If human provided explicit correct result, use that
    if correct_result:
        if '无异常' in correct_result or '不告警' in correct_result:
            return '无异常'
        else:
            # Parse the correct result (e.g., "敏感咨询-重大客诉")
            return correct_result.strip()
    
    # If no explicit correct result, analyze human review
    if human_review:
        # Keywords indicating the model prediction is correct
        correct_keywords = [
            '无异常',
            '业务模板',
            '正常业务',
            '公共邮箱',
            '官方邮箱'
        ]
        
        # Keywords indicating classification errors
        classification_error_keywords = [
            '大分类不对',
            '分类不对',
            '实际为用户咨询',
            '实际为客服回复',
            '不是政府机构咨询',
            '而非辱骂信息',
            '内容实际是邮件系统'
        ]
        
        # Check if human says it's correct
        if any(keyword in human_review for keyword in correct_keywords):
            return model_prediction
        
        # Check if human says classification is wrong
        if any(keyword in human_review for keyword in classification_error_keywords):
            return '无异常'
        
        # Special case: potential risk
        if '潜在风险' in human_review:
            return model_prediction  # Keep original classification but mark as risky
    
    # Default: trust the model prediction
    return model_prediction

def create_ground_truth_by_category():
    """Create ground truth dataset organized by categories."""
    
    # Load processed annotations
    annotations_file = Path("data/processed_gt/annotations.json")
    if not annotations_file.exists():
        print("Error: annotations.json not found. Please run process_gt_data_only.py first.")
        return None
    
    with open(annotations_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # Group by case_id
    case_groups = defaultdict(list)
    for ann in annotations:
        case_groups[ann['case_id']].append(ann)
    
    # Process each case
    gt_dataset = []
    stats = {
        'total_cases': len(case_groups),
        'by_final_category': defaultdict(int),
        'by_model_category': defaultdict(int),
        'corrections_made': 0,
        'model_correct': 0,
        'category_mapping': defaultdict(list)  # model_pred -> final_gt
    }
    
    for case_id, case_annotations in case_groups.items():
        # Sort by msg_id to maintain order
        case_annotations.sort(key=lambda x: x['msg_id'])
        
        # Build clean messages
        clean_messages = []
        for ann in case_annotations:
            clean_content = clean_html_content(ann['original_msg'])
            if clean_content:
                clean_messages.append({
                    'id': ann['msg_id'],
                    'type': ann['user_type'],
                    'content': clean_content
                })
        
        if not clean_messages:
            continue
        
        # Get the primary annotation (first one with labels)
        primary_annotation = None
        for ann in case_annotations:
            if ann.get('human_review') or ann.get('correct_result'):
                primary_annotation = ann
                break
        
        if not primary_annotation:
            continue
        
        # Determine model prediction and final GT
        model_type = primary_annotation.get('f_type', '')
        model_sub_type = primary_annotation.get('f_sub_type', '')
        model_prediction = f"{model_type}-{model_sub_type}" if model_sub_type else model_type
        
        final_gt = determine_final_ground_truth(primary_annotation)
        
        # Update statistics
        stats['by_model_category'][model_prediction] += 1
        stats['by_final_category'][final_gt] += 1
        stats['category_mapping'][model_prediction].append(final_gt)
        
        if model_prediction != final_gt:
            stats['corrections_made'] += 1
        else:
            stats['model_correct'] += 1
        
        # Create dataset entry
        dataset_entry = {
            'case_id': case_id,
            'messages': clean_messages,
            'model_prediction': {
                'type': model_type,
                'sub_type': model_sub_type,
                'full_category': model_prediction
            },
            'ground_truth': final_gt,
            'human_annotations': {
                'human_review': primary_annotation.get('human_review'),
                'correct_result': primary_annotation.get('correct_result'),
                'needs_optimization': primary_annotation.get('needs_optimization')
            },
            'source_files': list(set(ann['file_source'] for ann in case_annotations)),
            'message_count': len(clean_messages)
        }
        
        gt_dataset.append(dataset_entry)
    
    return gt_dataset, stats

def save_ground_truth_dataset(gt_dataset, stats, output_dir="data/ground_truth_by_category"):
    """Save the ground truth dataset organized by categories."""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Save full dataset
    with open(output_path / "full_dataset.json", 'w', encoding='utf-8') as f:
        json.dump(gt_dataset, f, indent=2, ensure_ascii=False)
    
    # Create conversation data for model input
    conversation_data = []
    for entry in gt_dataset:
        conversation_data.append({
            'case_id': entry['case_id'],
            'messages': entry['messages']
        })
    
    with open(output_path / "conversation_data.json", 'w', encoding='utf-8') as f:
        json.dump(conversation_data, f, indent=2, ensure_ascii=False)
    
    # Create labels by category
    labels_data = []
    for entry in gt_dataset:
        labels_data.append({
            'case_id': entry['case_id'],
            'model_prediction': entry['model_prediction']['full_category'],
            'ground_truth': entry['ground_truth'],
            'is_model_correct': entry['model_prediction']['full_category'] == entry['ground_truth']
        })
    
    with open(output_path / "labels_by_category.json", 'w', encoding='utf-8') as f:
        json.dump(labels_data, f, indent=2, ensure_ascii=False)
    
    # Group by categories for easier evaluation
    by_category = defaultdict(list)
    for entry in gt_dataset:
        gt_category = entry['ground_truth']
        by_category[gt_category].append({
            'case_id': entry['case_id'],
            'model_prediction': entry['model_prediction']['full_category'],
            'ground_truth': gt_category,
            'messages': entry['messages']
        })
    
    with open(output_path / "dataset_by_category.json", 'w', encoding='utf-8') as f:
        json.dump(dict(by_category), f, indent=2, ensure_ascii=False)
    
    # Save statistics
    stats_serializable = {}
    for key, value in stats.items():
        if isinstance(value, defaultdict):
            stats_serializable[key] = dict(value)
        else:
            stats_serializable[key] = value
    
    with open(output_path / "dataset_stats.json", 'w', encoding='utf-8') as f:
        json.dump(stats_serializable, f, indent=2, ensure_ascii=False)
    
    # Create summary report
    with open(output_path / "dataset_summary.md", 'w', encoding='utf-8') as f:
        f.write("# Ground Truth Dataset by Category\n\n")
        
        f.write("## Dataset Statistics\n")
        f.write(f"- Total cases: {stats['total_cases']}\n")
        f.write(f"- Model correct: {stats['model_correct']} ({stats['model_correct']/stats['total_cases']*100:.1f}%)\n")
        f.write(f"- Corrections made: {stats['corrections_made']} ({stats['corrections_made']/stats['total_cases']*100:.1f}%)\n\n")
        
        f.write("## Model Prediction Distribution\n")
        for category, count in sorted(stats['by_model_category'].items(), key=lambda x: x[1], reverse=True):
            f.write(f"- {category}: {count}\n")
        f.write("\n")
        
        f.write("## Final Ground Truth Distribution\n")
        for category, count in sorted(stats['by_final_category'].items(), key=lambda x: x[1], reverse=True):
            f.write(f"- {category}: {count}\n")
        f.write("\n")
        
        f.write("## Category Corrections\n")
        for model_pred, final_gts in stats['category_mapping'].items():
            unique_finals = list(set(final_gts))
            if len(unique_finals) > 1 or unique_finals[0] != model_pred:
                f.write(f"- {model_pred} → {unique_finals}\n")
        f.write("\n")
        
        f.write("## Files Generated\n")
        f.write("- `full_dataset.json`: Complete dataset with all information\n")
        f.write("- `conversation_data.json`: Clean conversation data for model input\n")
        f.write("- `labels_by_category.json`: Ground truth labels by category\n")
        f.write("- `dataset_by_category.json`: Dataset grouped by final categories\n")
        f.write("- `dataset_stats.json`: Detailed statistics\n\n")
        
        f.write("## Usage for Model Evaluation\n")
        f.write("1. Use `conversation_data.json` as input to your model\n")
        f.write("2. Compare model predictions with `labels_by_category.json`\n")
        f.write("3. Calculate accuracy by category and overall\n")
        f.write("4. Use `dataset_by_category.json` for category-specific analysis\n\n")
        
        f.write("## Sample Entries\n")
        for i, entry in enumerate(gt_dataset[:5]):
            f.write(f"\n### Case {entry['case_id']}\n")
            f.write(f"- Model prediction: {entry['model_prediction']['full_category']}\n")
            f.write(f"- Ground truth: {entry['ground_truth']}\n")
            f.write(f"- Correct: {'✓' if entry['model_prediction']['full_category'] == entry['ground_truth'] else '✗'}\n")
            f.write(f"- Human review: {entry['human_annotations']['human_review']}\n")
            f.write(f"- Messages: {entry['message_count']}\n")
    
    print(f"Ground truth dataset saved to {output_path}")
    return output_path

def main():
    """Main function."""
    print("Creating ground truth dataset by category...")
    
    # Create dataset
    gt_dataset, stats = create_ground_truth_by_category()
    
    if gt_dataset is None:
        print("Failed to create dataset")
        return
    
    # Save dataset
    output_path = save_ground_truth_dataset(gt_dataset, stats)
    
    # Print summary
    print(f"\n=== GROUND TRUTH DATASET BY CATEGORY ===")
    print(f"Total cases: {stats['total_cases']}")
    print(f"Model correct: {stats['model_correct']} ({stats['model_correct']/stats['total_cases']*100:.1f}%)")
    print(f"Corrections made: {stats['corrections_made']} ({stats['corrections_made']/stats['total_cases']*100:.1f}%)")
    
    print(f"\nTop model predictions:")
    for category, count in sorted(stats['by_model_category'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {category}: {count}")
    
    print(f"\nTop final categories:")
    for category, count in sorted(stats['by_final_category'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {category}: {count}")
    
    print(f"\nFiles saved to: {output_path}")

if __name__ == "__main__":
    main()
