#!/usr/bin/env python3
"""
Script to show sample processed data in a readable format.
"""

import json
from pathlib import Path

def clean_html_simple(text):
    """Simple HTML cleaning for display."""
    if not text:
        return ""
    # Remove HTML tags and decode entities
    import re
    text = re.sub(r'<[^>]+>', ' ', text)
    text = text.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
    text = text.replace('&quot;', '"').replace('&#39;', "'").replace('&nbsp;', ' ')
    text = re.sub(r'\s+', ' ', text).strip()
    return text[:200] + "..." if len(text) > 200 else text

def show_sample_conversations():
    """Show sample conversations in readable format."""
    conversations_file = Path("data/processed_gt/conversations.json")
    
    if not conversations_file.exists():
        print("Conversations file not found. Please run process_gt_data_only.py first.")
        return
    
    with open(conversations_file, 'r', encoding='utf-8') as f:
        conversations = json.load(f)
    
    print(f"=== SAMPLE CONVERSATIONS ({len(conversations)} total) ===\n")
    
    # Show first 5 conversations
    for i, conv in enumerate(conversations[:5]):
        print(f"--- Conversation {i+1}: Case {conv['case_id']} ---")
        print(f"Messages: {conv['message_count']}")
        print(f"Source files: {conv['source_files']}")
        
        if conv['ground_truth']:
            gt = conv['ground_truth']
            print(f"Ground Truth:")
            print(f"  - Human review: {gt['human_review']}")
            print(f"  - Correct result: {gt['correct_result']}")
            print(f"  - Needs optimization: {gt['needs_optimization']}")
            print(f"  - Original type: {gt['original_type']}")
            print(f"  - Original sub-type: {gt['original_sub_type']}")
        else:
            print("Ground Truth: None")
        
        print(f"Messages:")
        for j, msg in enumerate(conv['messages'][:3]):  # Show first 3 messages
            clean_msg = clean_html_simple(msg['msg'])
            print(f"  {j+1}. [{msg['type']}] {clean_msg}")
        
        if len(conv['messages']) > 3:
            print(f"  ... and {len(conv['messages']) - 3} more messages")
        
        print()

def show_annotation_stats():
    """Show annotation statistics."""
    analysis_file = Path("data/processed_gt/analysis.json")
    
    if not analysis_file.exists():
        print("Analysis file not found.")
        return
    
    with open(analysis_file, 'r', encoding='utf-8') as f:
        analysis = json.load(f)
    
    print(f"=== ANNOTATION STATISTICS ===")
    print(f"Total annotations: {analysis['total_annotations']}")
    print(f"Unique cases: {analysis['unique_cases']}")
    print(f"Unique messages: {analysis['unique_messages']}")
    
    print(f"\nUser Type Distribution:")
    for user_type, count in analysis['by_user_type'].items():
        print(f"  {user_type}: {count}")
    
    print(f"\nType Distribution:")
    for type_name, count in analysis['by_type'].items():
        print(f"  {type_name}: {count}")
    
    print(f"\nSub-Type Distribution:")
    for sub_type, count in analysis['by_sub_type'].items():
        print(f"  {sub_type}: {count}")
    
    print(f"\nTop Human Review Categories:")
    sorted_reviews = sorted(analysis['human_review_categories'].items(), 
                          key=lambda x: x[1], reverse=True)
    for category, count in sorted_reviews[:10]:
        print(f"  {category}: {count}")
    
    print(f"\nCorrect Result Categories:")
    for category, count in sorted(analysis['correct_result_categories'].items(), 
                                key=lambda x: x[1], reverse=True):
        print(f"  {category}: {count}")
    
    print(f"\nNeeds Optimization:")
    for category, count in analysis['needs_optimization'].items():
        print(f"  {category}: {count}")

def main():
    """Main function."""
    show_annotation_stats()
    print("\n" + "="*80 + "\n")
    show_sample_conversations()

if __name__ == "__main__":
    main()
