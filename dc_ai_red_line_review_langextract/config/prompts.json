{"description": "提示词配置文件 - 用于AI模型的各类提示词模板", "version": "1.0", "prompts": {"extraction_prompt": "Extract sensitive content from customer service conversations.\nIdentify and extract text that matches the following categories:\n\n1. **consulting_company_info**: Company information inquiries (address, location, licensing)\n2. **selling_user_info**: Offers to buy/sell user data or account information  \n3. **negative_news**: Inquiries about negative rumors or scandals about the company\n4. **major_complaints**: Extremely negative complaints with threats of exposure\n5. **request_contact_information**: Requests for personal contact details\n6. **spam_messages**: Abusive or offensive language from customer service\n\nExtract EXACT text from the conversation. Do not paraphrase or modify.\nPreserve original language (Chinese, English, etc.).", "extraction_classes": ["consulting_company_info", "selling_user_info", "negative_news", "major_complaints", "request_contact_information", "spam_messages"], "category_descriptions": {"consulting_company_info": {"name": "咨询公司信息", "description": "Company information inquiries (address, location, licensing)"}, "selling_user_info": {"name": "兜售用户信息", "description": "Offers to buy/sell user data or account information"}, "negative_news": {"name": "负面新闻", "description": "Inquiries about negative rumors or scandals about the company"}, "major_complaints": {"name": "重大客诉", "description": "Extremely negative complaints with threats of exposure"}, "request_contact_information": {"name": "索要联系方式", "description": "Requests for personal contact details"}, "spam_messages": {"name": "辱骂信息", "description": "Abusive or offensive language from customer service"}}}}