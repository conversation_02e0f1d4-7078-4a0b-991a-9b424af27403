import argparse
import sys
from pathlib import Path

from config_loader import get_config_loader

from dc_ai_red_line_review_langextract.utils import get_logger

# Initialize logger
logger = get_logger(__name__)


class ConfigManager:
    """Manages loading, validation, and backup of configuration files."""

    def __init__(self):
        """Initializes the ConfigManager."""
        self.config_loader = get_config_loader()
        self.config_dir = Path(__file__).parent / "config"

    def validate_config(self) -> bool:
        """Validates all configuration files.

        Returns:
            True if all configurations are valid, False otherwise.
        """
        logger.info("🔍 Validating configuration files...")

        try:
            # Validate keyword configuration
            keywords = self.config_loader.load_keywords()
            logger.info(f"✓ keywords.json: {len(keywords)} categories")

            # Validate prompt configuration
            prompts = self.config_loader.load_prompts()
            logger.info(f"✓ prompts.json: {len(prompts)} config items")

            # Validate example configuration
            examples = self.config_loader.load_examples()
            logger.info(f"✓ examples.json: {len(examples)} examples")

            # Validate extraction functionality
            extraction_prompt = self.config_loader.get_extraction_prompt()
            extraction_classes = self.config_loader.get_extraction_classes()
            extraction_examples = self.config_loader.get_extraction_examples()

            logger.info(f"✓ Extraction prompt: {len(extraction_prompt)} characters")
            logger.info(f"✓ Extraction classes: {len(extraction_classes)} classes")
            logger.info(f"✓ Extraction examples: {len(extraction_examples)} examples")

            logger.info("✅ All configuration files validated successfully!")
            return True

        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            return False

    def show_config_summary(self):
        """Displays a summary of the configuration files."""
        logger.info("📋 Configuration File Summary:")
        logger.info("=" * 50)

        try:
            # Keyword summary
            keywords = self.config_loader.load_keywords()
            logger.info("\n🔑 Keyword Configuration (keywords.json):")
            for category, items in keywords.items():
                if isinstance(items, list):
                    logger.info(f"  - {category}: {len(items)} keywords")
                elif isinstance(items, dict):
                    logger.info(f"  - {category}:")
                    for sub_cat, sub_items in items.items():
                        logger.info(f"    - {sub_cat}: {len(sub_items)} items")

            # Prompt summary
            prompts = self.config_loader.load_prompts()
            logger.info("\n💬 Prompt Configuration (prompts.json):")
            logger.info(f"  - Extraction prompt: {len(prompts.get('extraction_prompt', ''))} characters")
            logger.info(f"  - Extraction classes: {len(prompts.get('extraction_classes', []))} classes")
            logger.info(
                f"  - Category descriptions: {len(prompts.get('category_descriptions', {}))} descriptions"
            )

            # Example summary
            examples = self.config_loader.load_examples()
            logger.info("\n📝 Example Configuration (examples.json):")
            logger.info(f"  - Total examples: {len(examples)}")

            total_extractions = sum(len(ex.get("extractions", [])) for ex in examples)
            logger.info(f"  - Total extractions: {total_extractions}")

            # Tally by class
            class_counts = {}
            for example in examples:
                for extraction in example.get("extractions", []):
                    cls = extraction.get("extraction_class", "unknown")
                    class_counts[cls] = class_counts.get(cls, 0) + 1

            logger.info("  - Distribution by class:")
            for cls, count in sorted(class_counts.items()):
                logger.info(f"    - {cls}: {count} instances")

        except Exception as e:
            logger.error(f"❌ Could not display configuration summary: {e}")

    def backup_config(self, backup_dir: str = None):
        """Backs up the configuration files.

        Args:
            backup_dir: The directory to back up the files to.
        """
        if backup_dir is None:
            backup_dir = self.config_dir.parent / "config_backup"

        backup_path = Path(backup_dir)
        backup_path.mkdir(exist_ok=True)

        logger.info(f"💾 Backing up configuration files to: {backup_path}")

        config_files = ["keywords.json", "prompts.json", "examples.json", "README.md"]

        for file_name in config_files:
            src_file = self.config_dir / file_name
            dst_file = backup_path / file_name

            if src_file.exists():
                dst_file.write_text(src_file.read_text(encoding="utf-8"), encoding="utf-8")
                logger.info(f"✓ Backed up: {file_name}")
            else:
                logger.warning(f"⚠️ File not found, skipping: {file_name}")

        logger.info("✅ Backup complete!")

    def clear_cache(self):
        """Clears the configuration cache."""
        logger.info("🧹 Clearing configuration cache...")
        self.config_loader.clear_cache()
        logger.info("✅ Cache cleared!")


def main():
    """Main function to run the configuration management script."""
    parser = argparse.ArgumentParser(description="Configuration file management tool")
    parser.add_argument(
        "action",
        choices=["validate", "summary", "backup", "clear-cache"],
        help="The action to perform",
    )
    parser.add_argument("--backup-dir", help="The directory for backups")

    args = parser.parse_args()

    manager = ConfigManager()

    if args.action == "validate":
        success = manager.validate_config()
        sys.exit(0 if success else 1)

    elif args.action == "summary":
        manager.show_config_summary()

    elif args.action == "backup":
        manager.backup_config(args.backup_dir)

    elif args.action == "clear-cache":
        manager.clear_cache()


if __name__ == "__main__":
    main()