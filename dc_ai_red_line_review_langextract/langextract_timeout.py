import os
import signal
from contextlib import contextmanager
from typing import Any

import langextract as lx
from dotenv import load_dotenv
from langextract.providers.openai import OpenAILanguageModel

from dc_ai_red_line_review_langextract.extraction_rules import (
    get_extraction_examples,
    get_extraction_prompt,
)
from dc_ai_red_line_review_langextract.utils import (
    find_text_in_messages,
    get_logger,
    timing_decorator,
)

load_dotenv(override=True)


class TimeoutError(Exception):
    """Timeout exception for langextract processing."""

    pass


@contextmanager
def timeout(seconds):
    """Context manager for timeout handling."""

    def timeout_handler(signum, frame):
        raise TimeoutError(f"Operation timed out after {seconds} seconds")

    # Set the signal handler
    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(seconds)

    try:
        yield
    finally:
        # Restore the old handler
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)


class TimeoutLangExtractProcessor:
    """LangExtract processor with timeout and retry mechanisms."""

    def __init__(self, timeout_seconds: int = 30):
        self.logger = get_logger(module_name="timeout_langextract_processor")
        self.timeout_seconds = timeout_seconds

        # Initialize the language model with timeout-friendly settings
        self.model = OpenAILanguageModel(
            model_id=os.environ["MODEL_NAME"],
            api_key=os.environ["QWQ_API_KEY"],
            base_url=os.environ["QWQ_BASE_URL"],
            max_workers=2,  # Conservative worker count
        )

        # Get extraction configuration
        self.prompt = get_extraction_prompt()
        self.examples = get_extraction_examples()

        self.logger.info(
            f"Timeout LangExtract processor initialized with {timeout_seconds}s timeout"
        )

    def _extract_with_timeout(
        self, text: str, chunk_size: int = 2000
    ) -> lx.data.AnnotatedDocument:
        """Extract with timeout protection."""
        try:
            with timeout(self.timeout_seconds):
                result = lx.extract(
                    text_or_documents=text,
                    prompt_description=self.prompt,
                    examples=self.examples,
                    model=self.model,
                    extraction_passes=1,
                    max_workers=2,  # Conservative setting
                    max_char_buffer=chunk_size,
                    fence_output=True,
                    use_schema_constraints=False,
                    debug=False,
                )
                return result
        except TimeoutError as e:
            self.logger.warning(f"Extraction timed out: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Extraction failed: {e}")
            raise

    def _split_text_smart(self, text: str, max_size: int = 2000) -> list[str]:
        """Smart text splitting that preserves message boundaries."""
        if len(text) <= max_size:
            return [text]

        # Try to split at message boundaries
        lines = text.split("\n")
        chunks = []
        current_chunk = ""

        for line in lines:
            # Check if adding this line would exceed the limit
            if len(current_chunk) + len(line) + 1 > max_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = line
                else:
                    # Single line is too long, force split
                    chunks.append(line[:max_size])
                    current_chunk = line[max_size:]
            else:
                if current_chunk:
                    current_chunk += "\n" + line
                else:
                    current_chunk = line

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    @timing_decorator
    def extract_sensitive_content_with_timeout(
        self, text: str
    ) -> lx.data.AnnotatedDocument:
        """Extract sensitive content with timeout and retry logic."""
        self.logger.info(
            f"Starting timeout-protected extraction for {len(text):,} characters"
        )

        # Try with normal chunk size first
        try:
            return self._extract_with_timeout(text, chunk_size=3000)
        except TimeoutError:
            self.logger.warning(
                "Normal extraction timed out, trying with smaller chunks"
            )

            # Split into smaller chunks and process separately
            chunks = self._split_text_smart(text, max_size=1500)
            self.logger.info(f"Split text into {len(chunks)} smaller chunks")

            all_extractions = []

            for i, chunk in enumerate(chunks):
                self.logger.info(f"Processing chunk {i + 1}/{len(chunks)}")
                try:
                    chunk_result = self._extract_with_timeout(chunk, chunk_size=1500)
                    all_extractions.extend(chunk_result.extractions)
                except TimeoutError:
                    self.logger.warning(f"Chunk {i + 1} timed out, skipping")
                    continue
                except Exception as e:
                    self.logger.error(f"Chunk {i + 1} failed: {e}")
                    continue

            # Combine results
            return lx.data.AnnotatedDocument(
                text=text,
                extractions=all_extractions,
                metadata={"processing": "chunked_with_timeout"},
            )

    @timing_decorator
    def extract_with_fallback(self, text: str) -> lx.data.AnnotatedDocument:
        """Extract with multiple fallback strategies."""
        strategies = [
            {"chunk_size": 3000, "workers": 2, "name": "normal"},
            {"chunk_size": 2000, "workers": 1, "name": "reduced"},
            {"chunk_size": 1000, "workers": 1, "name": "minimal"},
        ]

        for strategy in strategies:
            self.logger.info(f"Trying {strategy['name']} strategy")
            try:
                with timeout(self.timeout_seconds):
                    result = lx.extract(
                        text_or_documents=text,
                        prompt_description=self.prompt,
                        examples=self.examples,
                        model=self.model,
                        extraction_passes=1,
                        max_workers=strategy["workers"],
                        max_char_buffer=strategy["chunk_size"],
                        fence_output=True,
                        use_schema_constraints=False,
                        debug=False,
                    )
                    self.logger.info(f"Success with {strategy['name']} strategy")
                    return result
            except TimeoutError:
                self.logger.warning(f"{strategy['name']} strategy timed out")
                continue
            except Exception as e:
                self.logger.warning(f"{strategy['name']} strategy failed: {e}")
                continue

        # If all strategies fail, return empty result
        self.logger.error("All extraction strategies failed")
        return lx.data.AnnotatedDocument(
            text=text, extractions=[], metadata={"processing": "all_strategies_failed"}
        )

    def convert_to_legacy_format(
        self,
        extraction_result: lx.data.AnnotatedDocument,
        messages: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """Convert langextract results to legacy format."""
        self.logger.info("Converting timeout-protected results to legacy format")

        # Initialize individual category results
        individual_results = {
            "consulting_company_info": {
                "hit_rule": False,
                "values": [],
                "type": "咨询公司信息",
            },
            "selling_user_info": {
                "hit_rule": False,
                "values": [],
                "type": "兜售用户信息",
            },
            "negative_news": {"hit_rule": False, "values": [], "type": "负面新闻"},
            "major_complaints": {"hit_rule": False, "values": [], "type": "重大客诉"},
            "request_contact_information": {
                "hit_rule": False,
                "values": [],
                "type": "索要联系方式",
            },
            "spam_messages": {"hit_rule": False, "values": [], "type": "辱骂信息"},
        }

        # Process extractions
        for extraction in extraction_result.extractions:
            category = extraction.extraction_class
            text = extraction.extraction_text

            if category in individual_results:
                individual_results[category]["values"].append(text)
                individual_results[category]["hit_rule"] = True

        # Remove duplicates while preserving order
        for category in individual_results:
            values = individual_results[category]["values"]
            individual_results[category]["values"] = list(dict.fromkeys(values))

        # Add matched_ids for each category
        self._add_matched_ids(individual_results, messages)

        # Create summary categories
        sensitive_inquiry = []
        sensitive_reply = []

        # Map to summary categories
        inquiry_categories = [
            "consulting_company_info",
            "selling_user_info",
            "negative_news",
            "major_complaints",
        ]
        reply_categories = ["request_contact_information", "spam_messages"]

        for category in inquiry_categories:
            category_result = individual_results[category].copy()
            sensitive_inquiry.append(category_result)

        for category in reply_categories:
            category_result = individual_results[category].copy()
            sensitive_reply.append(category_result)

        return {
            "sensitive_inquiry": sensitive_inquiry,
            "sensitive_reply": sensitive_reply,
        }

    def _add_matched_ids(
        self, result: dict[str, Any], messages: list[dict[str, Any]]
    ) -> None:
        """Add matched_ids field to each category result."""
        for category, data in result.items():
            if isinstance(data, dict) and "values" in data:
                matched_ids = []

                for value in data["values"]:
                    ids = find_text_in_messages(value, messages)
                    matched_ids.append(ids)

                data["matched_ids"] = matched_ids

                self.logger.debug(
                    f"Category {category}: {len(data['values'])} values, "
                    f"{sum(len(ids) for ids in matched_ids)} total matches"
                )
