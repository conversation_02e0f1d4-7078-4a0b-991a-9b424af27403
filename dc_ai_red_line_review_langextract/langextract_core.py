import os
from typing import Any

import langextract as lx
from dotenv import load_dotenv
from langextract.providers.openai import OpenAILanguageModel

from dc_ai_red_line_review_langextract.extraction_rules import (
    get_extraction_examples,
    get_extraction_prompt,
)
from dc_ai_red_line_review_langextract.utils import (
    find_text_in_messages,
    get_logger,
    timing_decorator,
)

load_dotenv(override=True)


class LangExtractProcessor:
    """Core processor using langextract for sensitive content detection."""

    def __init__(self):
        self.logger = get_logger(module_name="langextract_processor")

        # Initialize the language model
        self.model = OpenAILanguageModel(
            model_id=os.environ["MODEL_NAME"],
            api_key=os.environ["QWQ_API_KEY"],
            base_url=os.environ["QWQ_BASE_URL"],
        )

        # Get extraction configuration
        self.prompt = get_extraction_prompt()
        self.examples = get_extraction_examples()

        self.logger.info("LangExtract processor initialized")

    @timing_decorator
    def extract_sensitive_content(self, text: str) -> lx.data.AnnotatedDocument:
        """Extract sensitive content using langextract.

        Args:
            text: Formatted conversation text

        Returns:
            LangExtract AnnotatedDocument result
        """
        self.logger.info(
            f"Starting langextract processing for {len(text):,} characters"
        )

        try:
            result = lx.extract(
                text_or_documents=text,
                prompt_description=self.prompt,
                examples=self.examples,
                model=self.model,
                extraction_passes=1,  # Reduced from 3 to 1 for 3x speed improvement
                max_workers=3,  # Reduced to prevent resource contention
                max_char_buffer=3000,  # Smaller chunks for faster processing
                fence_output=True,
                use_schema_constraints=False,
                debug=False,
            )

            self.logger.info(
                f"LangExtract completed: {len(result.extractions)} extractions from {len(result.text):,} characters"
            )

            return result

        except Exception as e:
            self.logger.error(f"LangExtract processing failed: {e}")
            raise

    def convert_to_legacy_format(
        self,
        extraction_result: lx.data.AnnotatedDocument,
        messages: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """Convert langextract results to legacy format.

        Args:
            extraction_result: LangExtract AnnotatedDocument result
            messages: Original message list for matched_ids

        Returns:
            Dictionary in legacy format with sensitive_inquiry and sensitive_reply
        """
        self.logger.info("Converting langextract results to legacy format")

        # Initialize individual category results (these will be included in final output)
        individual_results = {
            "consulting_company_info": {
                "hit_rule": False,
                "values": [],
                "type": "咨询公司信息",
            },
            "selling_user_info": {
                "hit_rule": False,
                "values": [],
                "type": "兜售用户信息",
            },
            "negative_news": {"hit_rule": False, "values": [], "type": "负面新闻"},
            "major_complaints": {"hit_rule": False, "values": [], "type": "重大客诉"},
            "request_contact_information": {
                "hit_rule": False,
                "values": [],
                "type": "索要联系方式",
            },
            "spam_messages": {"hit_rule": False, "values": [], "type": "辱骂信息"},
        }

        # Process extractions
        for extraction in extraction_result.extractions:
            category = extraction.extraction_class
            text = extraction.extraction_text

            if category in individual_results:
                individual_results[category]["values"].append(text)
                individual_results[category]["hit_rule"] = True

        # Remove duplicates while preserving order
        for category in individual_results:
            values = individual_results[category]["values"]
            individual_results[category]["values"] = list(dict.fromkeys(values))

        # Add matched_ids for each category
        self._add_matched_ids(individual_results, messages)

        # Create summary categories in the exact format expected by the original system
        sensitive_inquiry = []
        sensitive_reply = []

        # Map to summary categories - always include all categories, even if empty
        inquiry_categories = [
            "consulting_company_info",
            "selling_user_info",
            "negative_news",
            "major_complaints",
        ]
        reply_categories = ["request_contact_information", "spam_messages"]

        # Add all inquiry categories to sensitive_inquiry (matching original behavior)
        for category in inquiry_categories:
            category_result = individual_results[category].copy()
            sensitive_inquiry.append(category_result)

        # Add all reply categories to sensitive_reply (matching original behavior)
        for category in reply_categories:
            category_result = individual_results[category].copy()
            sensitive_reply.append(category_result)

        # Return ONLY the summary categories, not individual ones
        # This matches the original system's output format
        return {
            "sensitive_inquiry": sensitive_inquiry,
            "sensitive_reply": sensitive_reply,
        }

    def _add_matched_ids(
        self, result: dict[str, Any], messages: list[dict[str, Any]]
    ) -> None:
        """Add matched_ids field to each category result.

        Args:
            result: Result dictionary to modify
            messages: Original message list
        """
        for category, data in result.items():
            if isinstance(data, dict) and "values" in data:
                matched_ids = []

                for value in data["values"]:
                    # Find which messages contain this extracted text
                    ids = find_text_in_messages(value, messages)
                    matched_ids.append(ids)

                data["matched_ids"] = matched_ids

                self.logger.debug(
                    f"Category {category}: {len(data['values'])} values, "
                    f"{sum(len(ids) for ids in matched_ids)} total matches"
                )
