import json
from pathlib import Path
from typing import Any

import langextract as lx


class ConfigLoader:
    """Configuration loader for keyword, prompt, and example files."""

    def __init__(self, config_dir: str = None):
        """Initializes the configuration loader.

        Args:
            config_dir: Path to the configuration directory. Defaults to the 'config'
                        directory within the current module.
        """
        if config_dir is None:
            # Default to the 'config' directory in the current module
            current_dir = Path(__file__).parent
            config_dir = current_dir / "config"

        self.config_dir = Path(config_dir)
        self._keywords_cache = None
        self._prompts_cache = None
        self._examples_cache = None

    def load_keywords(self) -> dict[str, Any]:
        """Loads the keyword configuration.

        Returns:
            A dictionary containing the keyword configuration.
        """
        if self._keywords_cache is None:
            keywords_file = self.config_dir / "keywords.json"
            with open(keywords_file, encoding="utf-8") as f:
                config = json.load(f)
                self._keywords_cache = config.get("keywords", {})

        return self._keywords_cache

    def load_prompts(self) -> dict[str, Any]:
        """Loads the prompt configuration.

        Returns:
            A dictionary containing the prompt configuration.
        """
        if self._prompts_cache is None:
            prompts_file = self.config_dir / "prompts.json"
            with open(prompts_file, encoding="utf-8") as f:
                config = json.load(f)
                self._prompts_cache = config.get("prompts", {})

        return self._prompts_cache

    def load_examples(self) -> list[dict[str, Any]]:
        """Loads the example configuration.

        Returns:
            A list of dictionaries, each representing an example.
        """
        if self._examples_cache is None:
            examples_file = self.config_dir / "examples.json"
            with open(examples_file, encoding="utf-8") as f:
                config = json.load(f)
                self._examples_cache = config.get("examples", [])

        return self._examples_cache

    def get_extraction_prompt(self) -> str:
        """Gets the extraction prompt string.

        Returns:
            The extraction prompt string.
        """
        prompts = self.load_prompts()
        return prompts.get("extraction_prompt", "")

    def get_extraction_classes(self) -> list[str]:
        """Gets the list of extraction classes.

        Returns:
            A list of extraction class names.
        """
        prompts = self.load_prompts()
        return prompts.get("extraction_classes", [])

    def get_extraction_examples(self) -> list[lx.data.ExampleData]:
        """Gets the extraction example data.

        Returns:
            A list of LangExtract ExampleData objects.
        """
        examples_data = self.load_examples()
        examples = []

        for example in examples_data:
            text = example.get("text", "")
            extractions_data = example.get("extractions", [])

            extractions = []
            for ext_data in extractions_data:
                extraction = lx.data.Extraction(
                    extraction_class=ext_data.get("extraction_class", ""),
                    extraction_text=ext_data.get("extraction_text", ""),
                    attributes=ext_data.get("attributes", {}),
                )
                extractions.append(extraction)

            example_data = lx.data.ExampleData(text=text, extractions=extractions)
            examples.append(example_data)

        return examples

    def get_keywords_for_category(self, category: str) -> Any:
        """Gets keywords for a specific category.

        Args:
            category: The name of the keyword category.

        Returns:
            The keyword data for the specified category.
        """
        keywords = self.load_keywords()
        return keywords.get(category, [])

    def clear_cache(self):
        """Clears the cache, forcing a reload of the configuration files."""
        self._keywords_cache = None
        self._prompts_cache = None
        self._examples_cache = None


# Global instance of the configuration loader
_config_loader = None


def get_config_loader() -> ConfigLoader:
    """Gets the global instance of the configuration loader.

    Returns:
        The ConfigLoader instance.
    """
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader