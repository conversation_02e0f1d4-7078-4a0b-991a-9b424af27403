import json
import os

from dotenv import load_dotenv
from pydantic import BaseModel, ValidationError

from dc_ai_red_line_review_langextract.config_loader import get_config_loader
from dc_ai_red_line_review_langextract.keyword_matcher import KeywordMatcher
from dc_ai_red_line_review_langextract.langextract_core import LangExtractProcessor

# from dc_ai_red_line_review_langextract.langextract_timeout import (
#     TimeoutLangExtractProcessor,
# )
from dc_ai_red_line_review_langextract.utils import (
    get_logger,
    messages_to_text,
    timing_decorator,
)

load_dotenv(override=True)


# Pydantic model for message validation
class MessageItem(BaseModel):
    id: int
    type: str
    msg: str


class BasicPipeline:
    def __init__(self):
        self.logger = get_logger(module_name="red_line_review")

        # Load configuration using config loader
        self.config_loader = get_config_loader()

        # Load prompt configuration (fallback to old method if needed)
        try:
            # Try to load keywords from new config system
            self.keywords_config = self.config_loader.load_keywords()
        except Exception as e:
            self.logger.warning(
                f"Failed to load from new config system, falling back to old method: {e}"
            )
            # Fallback to old configuration method
            if os.environ.get("PROMPT_SOURCE") == "local":
                with open(
                    os.path.join(os.environ["PROMPT_PATH"], "prompt.json")
                ) as file:
                    self.prompt_dict = json.load(file)
            else:
                self.prompt_dict = json.loads(os.environ["PROMPT_DICT"])
            self.keywords_config = self.prompt_dict

        self.logger.info("Initializing pipeline components")

        # Initialize processors
        self.langextract_processor = LangExtractProcessor()
        self.keyword_matcher = KeywordMatcher()

        self.logger.info("Pipeline initialized successfully")

    @timing_decorator
    def _validate_and_sort_messages(self, messages, caseId):
        """Validate and sort messages by 'id' using Pydantic. Raises ValueError if invalid."""
        if not isinstance(messages, list):
            raise ValueError(
                f"caseId {caseId}: 'messages' must be a list, got {type(messages)}"
            )
        if not messages:
            self.logger.warning(f"caseId {caseId}: empty messages list")
            return []
        try:
            msgs = [MessageItem(**msg) for msg in messages]
        except ValidationError as e:
            raise ValueError(f"caseId {caseId}: invalid messages data - {e}")

        # Sort by id and convert to dict
        return [m.model_dump() for m in sorted(msgs, key=lambda x: x.id)]

    @timing_decorator
    def _process_with_langextract(self, text: str, messages):
        """Process text using langextract for sensitive content detection.

        Args:
            text: Formatted conversation text
            messages: Original message list for matched_ids

        Returns:
            Processed results in legacy format
        """
        self.logger.info("Starting langextract processing")

        # Extract sensitive content using langextract
        extraction_result = self.langextract_processor.extract_sensitive_content(text)

        # Convert to legacy format
        langextract_results = self.langextract_processor.convert_to_legacy_format(
            extraction_result, messages
        )

        self.logger.info("LangExtract processing completed")
        return langextract_results

    @timing_decorator
    def _process_keyword_matching(self, text: str, messages):
        """Process keyword-based matching for legacy categories.

        Args:
            text: Text content to search
            messages: Original message list for matched_ids

        Returns:
            Keyword matching results
        """
        self.logger.info("Starting keyword matching")

        results = {}

        # Key contact review
        key_contact_result = self.keyword_matcher.key_contact_review(
            text, self.keywords_config["key_contact"]
        )
        key_contact_result = self.keyword_matcher.add_matched_ids_to_keyword_results(
            key_contact_result, messages
        )
        results["key_contact"] = key_contact_result

        # Government inquiry review
        government_result = self.keyword_matcher.government_inquiry_review(
            text, self.keywords_config["government_inquiry"]
        )
        government_result = self.keyword_matcher.add_matched_ids_to_keyword_results(
            government_result, messages
        )
        results["government_inquiry"] = government_result

        # Internal system review (using same logic as key_contact)
        internal_system_result = self.keyword_matcher.key_contact_review(
            text, self.keywords_config["internal_system"]
        )
        internal_system_result = (
            self.keyword_matcher.add_matched_ids_to_keyword_results(
                internal_system_result, messages
            )
        )
        results["internal_system"] = internal_system_result

        self.logger.info("Keyword matching completed")
        return results

    @timing_decorator
    def run(self, messages: list, caseId: str = ""):
        """Process and analyze messages using simplified langextract approach.

        Args:
            messages: List of message dictionaries
            caseId: Case identifier

        Returns:
            Analysis results in legacy format
        """
        self.logger.info(
            f"Processing caseId: {caseId} with {len(messages) if isinstance(messages, list) else 0} messages"
        )

        # Step 1: Validate and prepare messages
        try:
            msgs = self._validate_and_sort_messages(messages, caseId)
            if not msgs:
                return {"id": caseId, "review_res": {}}

            self.logger.info(f"Validated {len(msgs)} messages for caseId {caseId}")

        except ValueError as e:
            raise ValueError(f"Message validation failed: {e}")

        # Step 2: Convert messages to text
        text = messages_to_text(msgs)
        self.logger.info(f"Converted to text: {len(text):,} characters")

        # Step 3: Process with langextract
        try:
            langextract_results = self._process_with_langextract(text, msgs)
        except Exception as e:
            self.logger.error(f"LangExtract processing failed: {e}")
            # Provide empty results as fallback
            langextract_results = {
                "sensitive_inquiry": [],
                "sensitive_reply": [],
                "consulting_company_info": {
                    "hit_rule": False,
                    "values": [],
                    "matched_ids": [],
                },
                "selling_user_info": {
                    "hit_rule": False,
                    "values": [],
                    "matched_ids": [],
                },
                "negative_news": {"hit_rule": False, "values": [], "matched_ids": []},
                "major_complaints": {
                    "hit_rule": False,
                    "values": [],
                    "matched_ids": [],
                },
                "request_contact_information": {
                    "hit_rule": False,
                    "values": [],
                    "matched_ids": [],
                },
                "spam_messages": {"hit_rule": False, "values": [], "matched_ids": []},
            }

        # Step 4: Process keyword matching
        try:
            keyword_results = self._process_keyword_matching(text, msgs)
        except Exception as e:
            self.logger.error(f"Keyword matching failed: {e}")
            # Provide empty results as fallback
            keyword_results = {
                "key_contact": {"hit_rule": False, "values": [], "matched_ids": []},
                "government_inquiry": [],
                "internal_system": {"hit_rule": False, "values": [], "matched_ids": []},
            }

        # Step 5: Combine results
        final_results = {**langextract_results, **keyword_results}

        self.logger.info(f"Processing completed for caseId {caseId}")

        return {"id": caseId, "review_res": final_results}


if __name__ == "__main__":
    # Test with the same data from original main_pipe.py
    pipeline = BasicPipeline()
    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    from pprint import pprint

    pprint(pipeline.run(messages=messages, caseId=caseId))
