
import langextract as lx

from dc_ai_red_line_review_langextract.config_loader import get_config_loader


def get_extraction_prompt() -> str:
    """Get the main extraction prompt for sensitive content detection."""
    config_loader = get_config_loader()
    return config_loader.get_extraction_prompt()


def get_extraction_examples() -> list[lx.data.ExampleData]:
    """Get example data for training the extraction model."""
    config_loader = get_config_loader()
    return config_loader.get_extraction_examples()


def get_extraction_classes() -> list[str]:
    """Get the list of extraction classes for sensitive content."""
    config_loader = get_config_loader()
    return config_loader.get_extraction_classes()
