import logging
import os
import time
from collections.abc import Callable
from functools import wraps
from typing import Any

from dotenv import load_dotenv
from rich.console import Console
from rich.logging import Rich<PERSON>andler

load_dotenv(override=True)

# Dictionary to track configured loggers
_CONFIGURED_LOGGERS = {}


def get_logger(module_name: str) -> logging.Logger:
    """Configure and return a module-specific logger instance.

    Args:
        module_name: The name of the module to identify in logs

    Returns:
        logging.Logger: A configured logger with rich formatting
    """
    # Check if this logger was already configured
    if module_name in _CONFIGURED_LOGGERS:
        return _CONFIGURED_LOGGERS[module_name]

    # Create module-specific logs directory if LOG_PATH is set
    LOG_DIR = os.environ.get("LOG_PATH", "./logs")

    # Create a custom logger
    custom_logger = logging.getLogger(module_name)

    # Prevent log propagation to avoid duplicate logs
    custom_logger.propagate = False

    # Clear any existing handlers to avoid duplicates
    if custom_logger.hasHandlers():
        custom_logger.handlers.clear()

    # Set the level
    custom_logger.setLevel(logging.INFO)

    # Configure console output with Rich
    console_handler = RichHandler(
        rich_tracebacks=True,
        console=Console(stderr=False),
        tracebacks_show_locals=True,
        show_time=True,
        show_path=True,
    )
    console_handler.setLevel(logging.INFO)
    custom_logger.addHandler(console_handler)

    # Configure file handler for log files
    try:
        os.makedirs(LOG_DIR, exist_ok=True)
        file_handler = logging.FileHandler(
            os.path.join(LOG_DIR, f"{module_name}.log"), encoding="utf-8"
        )
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        file_handler.setFormatter(file_formatter)
        custom_logger.addHandler(file_handler)
    except Exception as e:
        # If file logging fails, continue with console only
        custom_logger.warning(f"Failed to set up file logging: {e}")

    # Store the configured logger
    _CONFIGURED_LOGGERS[module_name] = custom_logger

    return custom_logger


def timing_decorator(func: Callable) -> Callable:
    """Decorator to time function execution."""

    @wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        logger = get_logger("timing")
        logger.info(f"{func.__name__} completed in {end_time - start_time:.2f}s")

        return result

    return wrapper


def messages_to_text(messages: list[dict]) -> str:
    """Convert message list to formatted text for langextract processing.

    Args:
        messages: List of message dictionaries with id, type, msg fields

    Returns:
        Formatted text string preserving message structure
    """
    text_parts = []

    for msg in messages:
        msg_id = msg.get("id", "")
        msg_type = msg.get("type", "")
        msg_content = msg.get("msg", "")

        # Format: [ID:1][TYPE:USER] message content
        formatted_msg = f"[ID:{msg_id}][TYPE:{msg_type}] {msg_content}"
        text_parts.append(formatted_msg)

    return "\n".join(text_parts)


def extract_message_id_from_text(text: str) -> list[int]:
    """Extract message IDs from formatted text.

    Args:
        text: Text containing [ID:X] markers

    Returns:
        List of message IDs found in the text
    """
    import re

    # Find all [ID:X] patterns
    id_pattern = r"\[ID:(\d+)\]"
    matches = re.findall(id_pattern, text)

    return [int(match) for match in matches]


def find_text_in_messages(text: str, messages: list[dict]) -> list[int]:
    """Find which messages contain the given text.

    Args:
        text: Text to search for
        messages: List of message dictionaries

    Returns:
        List of message IDs that contain the text
    """
    matching_ids = []

    for msg in messages:
        msg_id = msg.get("id")
        msg_content = msg.get("msg", "")

        if text in msg_content:
            matching_ids.append(msg_id)

    return matching_ids
