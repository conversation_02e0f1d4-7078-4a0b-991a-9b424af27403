#!/usr/bin/env python3
"""
Template script for evaluating your model against the ground truth dataset.
Modify this script to integrate with your main_pipe.py
"""

import json
import sys
import os
from pathlib import Path
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report

# Add your model path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_ground_truth_data():
    """Load the ground truth dataset."""
    conversation_file = Path("data/ground_truth/conversation_data.json")
    labels_file = Path("data/ground_truth/labels.json")
    
    if not conversation_file.exists() or not labels_file.exists():
        print("Error: Ground truth files not found. Please run create_ground_truth_dataset.py first.")
        return None, None
    
    with open(conversation_file, 'r', encoding='utf-8') as f:
        conversations = json.load(f)
    
    with open(labels_file, 'r', encoding='utf-8') as f:
        labels = json.load(f)
    
    # Create a mapping from case_id to label
    label_map = {item['case_id']: item['has_issue'] for item in labels}
    
    return conversations, label_map

def predict_with_your_model(conversations):
    """
    Replace this function with your actual model prediction logic.
    
    Args:
        conversations: List of conversation data
        
    Returns:
        List of predictions (True for has_issue, False for no_issue)
    """
    predictions = []
    
    # TODO: Replace this with your actual model
    # Example integration with your main_pipe.py:
    
    try:
        # Uncomment and modify this section to use your model
        # from dc_ai_red_line_review_langextract.main_pipe import BasicPipeline
        # pipeline = BasicPipeline()
        
        for conv in conversations:
            case_id = conv['case_id']
            messages = conv['messages']
            
            # Convert to your model's expected format
            # Modify this based on your main_pipe.py input format
            model_messages = []
            for msg in messages:
                model_messages.append({
                    'id': msg['id'],
                    'type': msg['type'],
                    'msg': msg['content']  # or whatever field name your model expects
                })
            
            try:
                # TODO: Replace with your actual model call
                # result = pipeline.run(messages=model_messages, caseId=str(case_id))
                
                # TODO: Replace this logic with your actual prediction logic
                # This is just a placeholder that randomly predicts
                import random
                has_issue = random.choice([True, False])
                
                # TODO: Extract the actual prediction from your model result
                # Example logic (modify based on your model output):
                # has_issue = False
                # if result and 'review_res' in result:
                #     review_res = result['review_res']
                #     # Check if any category has alerts
                #     for category, data in review_res.items():
                #         if isinstance(data, dict) and data.get('hit_rule'):
                #             has_issue = True
                #             break
                #         elif isinstance(data, list) and data:
                #             has_issue = True
                #             break
                
                predictions.append(has_issue)
                
            except Exception as e:
                print(f"Error processing case {case_id}: {e}")
                predictions.append(False)  # Default to no issue
    
    except ImportError as e:
        print(f"Error importing model: {e}")
        print("Using random predictions for demonstration...")
        # Fallback to random predictions for demonstration
        import random
        predictions = [random.choice([True, False]) for _ in conversations]
    
    return predictions

def evaluate_predictions(y_true, y_pred, case_ids):
    """Evaluate model predictions against ground truth."""
    
    # Calculate metrics
    accuracy = accuracy_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred)
    
    # Confusion matrix
    cm = confusion_matrix(y_true, y_pred)
    
    # Classification report
    report = classification_report(y_true, y_pred, target_names=['no_issue', 'has_issue'])
    
    # Detailed results
    results = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': cm.tolist(),
        'classification_report': report,
        'total_samples': len(y_true),
        'true_positives': cm[1, 1],
        'true_negatives': cm[0, 0],
        'false_positives': cm[0, 1],
        'false_negatives': cm[1, 0]
    }
    
    # Find misclassified cases
    misclassified = []
    for i, (true_label, pred_label) in enumerate(zip(y_true, y_pred)):
        if true_label != pred_label:
            misclassified.append({
                'case_id': case_ids[i],
                'true_label': 'has_issue' if true_label else 'no_issue',
                'predicted_label': 'has_issue' if pred_label else 'no_issue'
            })
    
    results['misclassified_cases'] = misclassified
    
    return results

def save_evaluation_results(results, output_file="data/evaluation_results.json"):
    """Save evaluation results to file."""
    output_path = Path(output_file)
    output_path.parent.mkdir(exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"Evaluation results saved to {output_path}")

def print_evaluation_summary(results):
    """Print evaluation summary."""
    print("\n" + "="*60)
    print("MODEL EVALUATION RESULTS")
    print("="*60)
    
    print(f"Total samples: {results['total_samples']}")
    print(f"Accuracy: {results['accuracy']:.4f}")
    print(f"Precision: {results['precision']:.4f}")
    print(f"Recall: {results['recall']:.4f}")
    print(f"F1-Score: {results['f1_score']:.4f}")
    
    print(f"\nConfusion Matrix:")
    print(f"                Predicted")
    print(f"                No Issue  Has Issue")
    print(f"Actual No Issue    {results['true_negatives']:4d}      {results['false_positives']:4d}")
    print(f"Actual Has Issue   {results['false_negatives']:4d}      {results['true_positives']:4d}")
    
    print(f"\nClassification Report:")
    print(results['classification_report'])
    
    if results['misclassified_cases']:
        print(f"\nMisclassified Cases ({len(results['misclassified_cases'])}):")
        for case in results['misclassified_cases'][:10]:  # Show first 10
            print(f"  Case {case['case_id']}: True={case['true_label']}, Predicted={case['predicted_label']}")
        if len(results['misclassified_cases']) > 10:
            print(f"  ... and {len(results['misclassified_cases']) - 10} more")

def main():
    """Main evaluation function."""
    print("Loading ground truth data...")
    conversations, label_map = load_ground_truth_data()
    
    if conversations is None:
        return
    
    print(f"Loaded {len(conversations)} conversations")
    
    # Extract ground truth labels in the same order as conversations
    y_true = [label_map[conv['case_id']] for conv in conversations]
    case_ids = [conv['case_id'] for conv in conversations]
    
    print("Running model predictions...")
    y_pred = predict_with_your_model(conversations)
    
    if len(y_pred) != len(y_true):
        print(f"Error: Prediction count ({len(y_pred)}) doesn't match ground truth count ({len(y_true)})")
        return
    
    print("Evaluating predictions...")
    results = evaluate_predictions(y_true, y_pred, case_ids)
    
    # Print summary
    print_evaluation_summary(results)
    
    # Save results
    save_evaluation_results(results)
    
    print(f"\nEvaluation complete!")

if __name__ == "__main__":
    main()
