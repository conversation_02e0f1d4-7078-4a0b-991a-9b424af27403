#!/usr/bin/env python3
"""
Quick test for multi-label evaluation with first 5 cases.
"""

import json
import sys
import os
from pathlib import Path

# Add your model path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from evaluate_multilabel_model import (
    load_multilabel_ground_truth,
    predict_with_model,
    evaluate_multilabel_predictions,
    map_model_output_to_multilabel
)

def test_mapping_logic():
    """Test the mapping logic with sample data."""
    print("Testing mapping logic...")
    
    # Test case 1: Request contact information
    sample_output_1 = {
        "sensitive_reply": [
            {
                "hit_rule": True,
                "values": ["Please provide your phone number"],
                "type": "索要联系方式"
            }
        ],
        "sensitive_inquiry": [],
        "government_inquiry": [],
        "key_contact": {"hit_rule": False},
        "internal_system": {"hit_rule": False}
    }
    
    result_1 = map_model_output_to_multilabel(sample_output_1)
    print(f"Test 1 - Contact info: {result_1}")
    assert result_1['request_contact_information'] == True
    assert result_1['major_complaints'] == False
    
    # Test case 2: Major complaints
    sample_output_2 = {
        "sensitive_reply": [],
        "sensitive_inquiry": [
            {
                "hit_rule": True,
                "values": ["This is a major complaint"],
                "type": "重大客诉"
            }
        ],
        "government_inquiry": [],
        "key_contact": {"hit_rule": False},
        "internal_system": {"hit_rule": False}
    }
    
    result_2 = map_model_output_to_multilabel(sample_output_2)
    print(f"Test 2 - Major complaints: {result_2}")
    assert result_2['major_complaints'] == True
    assert result_2['request_contact_information'] == False
    
    print("✅ Mapping logic tests passed!")

def quick_evaluation_test():
    """Run quick evaluation on first 5 cases."""
    print("\n" + "="*60)
    print("QUICK MULTI-LABEL EVALUATION TEST")
    print("="*60)
    
    # Load first 5 cases
    conversations, label_map = load_multilabel_ground_truth(limit=5)
    
    if conversations is None:
        print("Failed to load data")
        return
    
    print(f"Loaded {len(conversations)} conversations for testing")
    
    # Show sample GT data
    print(f"\nSample Ground Truth:")
    for i, conv in enumerate(conversations[:3]):
        case_id = conv['case_id']
        if case_id in label_map:
            gt_labels = label_map[case_id]['labels']
            active_labels = [k for k, v in gt_labels.items() if v]
            print(f"Case {case_id}: {active_labels}")
    
    # Run predictions
    print(f"\nRunning model predictions...")
    predictions = predict_with_model(conversations)
    
    if not predictions:
        print("No predictions generated")
        return
    
    # Show sample predictions
    print(f"\nSample Predictions:")
    for pred in predictions[:3]:
        case_id = pred['case_id']
        pred_labels = pred['predictions']
        active_preds = [k for k, v in pred_labels.items() if v]
        print(f"Case {case_id}: {active_preds}")
    
    # Evaluate
    print(f"\nEvaluating predictions...")
    results = evaluate_multilabel_predictions(predictions, label_map)
    
    # Print results
    print(f"\n📊 QUICK TEST RESULTS")
    print(f"Exact Match Ratio: {results['exact_match_ratio']:.3f}")
    print(f"Hamming Loss: {results['hamming_loss']:.3f}")
    print(f"Jaccard Score: {results['jaccard_score']:.3f}")
    
    print(f"\n📈 AGGREGATE METRICS")
    print(f"Macro F1-Score: {results['macro_f1']:.3f}")
    print(f"Micro F1-Score: {results['micro_f1']:.3f}")
    
    # Show detailed case results
    print(f"\n🔍 DETAILED CASE RESULTS")
    for case_result in results['case_results']:
        case_id = case_result['case_id']
        gt_active = [k for k, v in case_result['ground_truth'].items() if v]
        pred_active = [k for k, v in case_result['predictions'].items() if v]
        match_status = "✅" if case_result['exact_match'] else "❌"
        
        print(f"{match_status} Case {case_id}:")
        print(f"    GT:   {gt_active}")
        print(f"    Pred: {pred_active}")
    
    # Show category metrics for active categories
    print(f"\n🎯 CATEGORY METRICS (Active Categories Only)")
    print(f"{'Category':<25} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<8}")
    print("-" * 70)
    
    for category, metrics in results["category_metrics"].items():
        if metrics["support"] > 0:
            print(f"{category:<25} {metrics['precision']:<10.3f} {metrics['recall']:<10.3f} {metrics['f1_score']:<10.3f} {metrics['support']:<8}")
    
    print(f"\n✅ Quick test completed!")

def main():
    """Main function for quick testing."""
    # Test mapping logic first
    test_mapping_logic()
    
    # Run quick evaluation
    quick_evaluation_test()

if __name__ == "__main__":
    main()
