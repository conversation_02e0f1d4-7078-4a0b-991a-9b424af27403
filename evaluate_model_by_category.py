#!/usr/bin/env python3
"""
Evaluate your model against the ground truth dataset by category.
This script compares your model's predictions with the corrected ground truth labels.
"""

import json
import sys
import os
from pathlib import Path
from collections import defaultdict
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    confusion_matrix,
    classification_report,
)

# Add your model path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def load_ground_truth_data():
    """Load the ground truth dataset by category."""
    conversation_file = Path("data/ground_truth_by_category/conversation_data.json")
    labels_file = Path("data/ground_truth_by_category/labels_by_category.json")

    if not conversation_file.exists() or not labels_file.exists():
        print(
            "Error: Ground truth files not found. Please run create_correct_ground_truth.py first."
        )
        return None, None

    with open(conversation_file, "r", encoding="utf-8") as f:
        conversations = json.load(f)

    with open(labels_file, "r", encoding="utf-8") as f:
        labels = json.load(f)

    # Create mappings
    label_map = {item["case_id"]: item for item in labels}

    return conversations, label_map


def predict_with_your_model(conversations):
    """
    Replace this function with your actual model prediction logic.

    Args:
        conversations: List of conversation data

    Returns:
        List of predictions in format: [{"case_id": xxx, "prediction": "category"}]
    """
    predictions = []

    # TODO: Replace this with your actual model
    # Example integration with your main_pipe.py:

    try:
        # Uncomment and modify this section to use your model
        # from dc_ai_red_line_review_langextract.main_pipe import BasicPipeline
        # pipeline = BasicPipeline()

        for conv in conversations:
            case_id = conv["case_id"]
            messages = conv["messages"]

            # Convert to your model's expected format
            model_messages = []
            for msg in messages:
                model_messages.append(
                    {
                        "id": msg["id"],
                        "type": msg["type"],
                        "msg": msg[
                            "content"
                        ],  # or whatever field name your model expects
                    }
                )

            try:
                # TODO: Replace with your actual model call
                # result = pipeline.run(messages=model_messages, caseId=str(case_id))

                # TODO: Extract prediction from your model result
                # This is a placeholder - replace with your actual logic
                predicted_category = "敏感回复-索要联系方式"  # Default prediction

                # TODO: Map your model output to categories
                # Example logic (modify based on your model output format):
                # if result and 'review_res' in result:
                #     review_res = result['review_res']
                #
                #     # Check different categories
                #     if review_res.get('sensitive_reply', {}).get('hit_rule'):
                #         if '索要联系方式' in str(review_res.get('sensitive_reply', {})):
                #             predicted_category = "敏感回复-索要联系方式"
                #         elif '辱骂信息' in str(review_res.get('sensitive_reply', {})):
                #             predicted_category = "敏感回复-辱骂信息"
                #     elif review_res.get('sensitive_inquiry', {}).get('hit_rule'):
                #         if '重大客诉' in str(review_res.get('sensitive_inquiry', {})):
                #             predicted_category = "敏感咨询-重大客诉"
                #         elif '负面新闻' in str(review_res.get('sensitive_inquiry', {})):
                #             predicted_category = "敏感咨询-负面新闻"
                #     elif review_res.get('government_inquiry', {}).get('hit_rule'):
                #         predicted_category = "政府咨询-政府机构"
                #     else:
                #         predicted_category = "无异常"

                predictions.append(
                    {"case_id": case_id, "prediction": predicted_category}
                )

            except Exception as e:
                print(f"Error processing case {case_id}: {e}")
                predictions.append(
                    {
                        "case_id": case_id,
                        "prediction": "无异常",  # Default to no issue
                    }
                )

    except ImportError as e:
        print(f"Error importing model: {e}")
        print("Using random predictions for demonstration...")
        # Fallback to random predictions for demonstration
        import random

        categories = ["敏感回复-索要联系方式", "敏感咨询-重大客诉", "无异常"]
        predictions = [
            {"case_id": conv["case_id"], "prediction": random.choice(categories)}
            for conv in conversations
        ]

    return predictions


def evaluate_by_category(predictions, label_map):
    """Evaluate predictions by category."""

    # Prepare data for evaluation
    y_true = []
    y_pred = []
    case_ids = []

    for pred in predictions:
        case_id = pred["case_id"]
        if case_id in label_map:
            y_true.append(label_map[case_id]["ground_truth"])
            y_pred.append(pred["prediction"])
            case_ids.append(case_id)

    # Get unique categories
    all_categories = sorted(list(set(y_true + y_pred)))

    # Overall metrics
    overall_accuracy = accuracy_score(y_true, y_pred)

    # Per-category metrics
    category_metrics = {}
    for category in all_categories:
        # Binary classification for this category
        y_true_binary = [1 if gt == category else 0 for gt in y_true]
        y_pred_binary = [1 if pred == category else 0 for pred in y_pred]

        if sum(y_true_binary) > 0:  # Only calculate if category exists in ground truth
            precision = precision_score(y_true_binary, y_pred_binary, zero_division=0)
            recall = recall_score(y_true_binary, y_pred_binary, zero_division=0)
            f1 = f1_score(y_true_binary, y_pred_binary, zero_division=0)

            category_metrics[category] = {
                "precision": precision,
                "recall": recall,
                "f1_score": f1,
                "support": sum(y_true_binary),
            }

    # Confusion matrix
    cm = confusion_matrix(y_true, y_pred, labels=all_categories)

    # Classification report
    report = classification_report(
        y_true, y_pred, labels=all_categories, zero_division=0
    )

    # Find misclassified cases by category
    misclassified_by_category = defaultdict(list)
    for i, (true_label, pred_label) in enumerate(zip(y_true, y_pred)):
        if true_label != pred_label:
            misclassified_by_category[true_label].append(
                {
                    "case_id": case_ids[i],
                    "predicted_as": pred_label,
                    "model_original": label_map[case_ids[i]]["model_prediction"],
                }
            )

    results = {
        "overall_accuracy": overall_accuracy,
        "total_samples": len(y_true),
        "category_metrics": category_metrics,
        "confusion_matrix": cm.tolist(),
        "category_labels": all_categories,
        "classification_report": report,
        "misclassified_by_category": dict(misclassified_by_category),
        "detailed_predictions": [
            {
                "case_id": case_ids[i],
                "ground_truth": y_true[i],
                "prediction": y_pred[i],
                "correct": y_true[i] == y_pred[i],
                "model_original": label_map[case_ids[i]]["model_prediction"],
            }
            for i in range(len(y_true))
        ],
    }

    return results


def print_evaluation_summary(results):
    """Print detailed evaluation summary."""
    print("\n" + "=" * 80)
    print("MODEL EVALUATION RESULTS BY CATEGORY")
    print("=" * 80)

    print(f"Overall Accuracy: {results['overall_accuracy']:.4f}")
    print(f"Total Samples: {results['total_samples']}")

    print(
        f"\n{'Category':<25} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<10}"
    )
    print("-" * 75)

    for category, metrics in results["category_metrics"].items():
        print(
            f"{category:<25} {metrics['precision']:<10.4f} {metrics['recall']:<10.4f} "
            f"{metrics['f1_score']:<10.4f} {metrics['support']:<10}"
        )

    print(f"\nConfusion Matrix:")
    print(f"Categories: {results['category_labels']}")
    for i, row in enumerate(results["confusion_matrix"]):
        print(f"{results['category_labels'][i]:<25}: {row}")

    print(f"\nClassification Report:")
    print(results["classification_report"])

    print(f"\nMisclassified Cases by Category:")
    for category, cases in results["misclassified_by_category"].items():
        print(f"\n{category} ({len(cases)} misclassified):")
        for case in cases[:5]:  # Show first 5
            print(
                f"  Case {case['case_id']}: Predicted as '{case['predicted_as']}' "
                f"(Original model: '{case['model_original']}')"
            )
        if len(cases) > 5:
            print(f"  ... and {len(cases) - 5} more")


def save_evaluation_results(
    results, output_file="data/evaluation_results_by_category.json"
):
    """Save evaluation results to file."""
    output_path = Path(output_file)
    output_path.parent.mkdir(exist_ok=True)

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    print(f"\nEvaluation results saved to {output_path}")


def main():
    """Main evaluation function."""
    print("Loading ground truth data...")
    conversations, label_map = load_ground_truth_data()

    if conversations is None:
        return

    print(f"Loaded {len(conversations)} conversations")

    print("Running model predictions...")
    predictions = predict_with_your_model(conversations)

    print("Evaluating predictions by category...")
    results = evaluate_by_category(predictions, label_map)

    # Print summary
    print_evaluation_summary(results)

    # Save results
    save_evaluation_results(results)

    print(f"\nEvaluation complete!")
    print(f"Key insights:")
    print(f"- Overall accuracy: {results['overall_accuracy']:.1%}")
    print(
        f"- Best performing category: {max(results['category_metrics'].items(), key=lambda x: x[1]['f1_score'])[0]}"
    )
    print(
        f"- Most challenging category: {min(results['category_metrics'].items(), key=lambda x: x[1]['f1_score'])[0]}"
    )


if __name__ == "__main__":
    main()
