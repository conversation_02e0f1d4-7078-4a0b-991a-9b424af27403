#!/usr/bin/env python3
"""时间分布分析脚本 - 输出到文件."""

import argparse
import json
import os
import statistics
import time
from datetime import datetime
from glob import glob

# from dc_ai_red_line_review import BasicPipeline
from dc_ai_red_line_review_langextract import BasicPipeline


def load_individual_cases():
    """加载所有individual cases数据."""
    cases_data = {}
    cases_dir = os.path.join("data", "individual_cases")

    if not os.path.exists(cases_dir):
        print(f"❌ 数据目录不存在: {cases_dir}")
        return {}

    json_files = glob(os.path.join(cases_dir, "*.json"))
    print(f"📂 找到 {len(json_files)} 个案例文件")

    for json_file in json_files:
        try:
            with open(json_file, encoding="utf-8") as f:
                case_data = json.load(f)
                case_id = case_data.get("caseId")
                messages = case_data.get("messages", [])
                if case_id and messages:
                    cases_data[case_id] = messages
        except Exception as e:
            print(f"❌ 读取文件失败 {json_file}: {e}")

    return cases_data


def analyze_and_process_dataset(data, dataset_name, output_file, engine: str = "basic"):  # noqa: C901
    """分析并处理数据集.

    engine: "basic" 使用 BasicPipeline；"lx" 使用 long_text.red_line_lx.run_review
    """
    output_file.write(f"\n{'=' * 60}\n")
    output_file.write(f"📊 {dataset_name} 时间分布分析\n")
    output_file.write(f"{'=' * 60}\n")

    pipeline = BasicPipeline()

    case_data = []

    for i, (case_id, messages) in enumerate(data.items(), 1):
        start_time = time.time()

        try:
            print(
                f"🔄 [{i:3d}/{len(data)}] 处理案例 {case_id} ({len(messages)} 消息)",
                end=" ",
            )

            # 处理案例
            _ = pipeline.run(messages=messages, caseId=case_id)

            duration = time.time() - start_time
            case_data.append(
                {
                    "case_id": case_id,
                    "message_count": len(messages),
                    "duration": duration,
                    "success": True,
                }
            )

            print(f"✅ {duration:.2f}s")

        except Exception as e:
            duration = time.time() - start_time
            case_data.append(
                {
                    "case_id": case_id,
                    "message_count": len(messages),
                    "duration": duration,
                    "success": False,
                    "error": str(e),
                }
            )
            print(f"❌ {duration:.2f}s")

    # 分析结果
    successful_cases = [c for c in case_data if c["success"]]
    durations = [c["duration"] for c in successful_cases]

    if not durations:
        output_file.write("❌ 没有成功的案例可供分析\n")
        return

    # 基本统计
    output_file.write("\n📈 基本统计信息:\n")
    output_file.write(f"   总案例数: {len(case_data)}\n")
    output_file.write(f"   成功案例: {len(successful_cases)}\n")
    output_file.write(f"   平均时间: {statistics.mean(durations):.2f} 秒\n")
    output_file.write(f"   中位数: {statistics.median(durations):.2f} 秒\n")
    if len(durations) > 1:
        output_file.write(f"   标准差: {statistics.stdev(durations):.2f} 秒\n")
    output_file.write(f"   最小值: {min(durations):.2f} 秒\n")
    output_file.write(f"   最大值: {max(durations):.2f} 秒\n")

    # 时间区间分布
    output_file.write("\n⏱️  时间区间分布:\n")
    time_ranges = [
        (0, 2, "极快"),
        (2, 5, "快速"),
        (5, 10, "正常"),
        (10, 20, "较慢"),
        (20, 50, "慢"),
        (50, float("inf"), "很慢"),
    ]

    for min_time, max_time, label in time_ranges:
        if max_time == float("inf"):
            count = len([d for d in durations if d >= min_time])
            if count > 0:
                percentage = count / len(durations) * 100
                output_file.write(
                    f"   {label} (≥{min_time}s): {count} 案例 ({percentage:.1f}%)\n"
                )
        else:
            count = len([d for d in durations if min_time <= d < max_time])
            if count > 0:
                percentage = count / len(durations) * 100
                output_file.write(
                    f"   {label} ({min_time}-{max_time}s): {count} 案例 ({percentage:.1f}%)\n"
                )

    # 消息数量与处理时间的关系
    output_file.write("\n💬 消息数量与处理时间关系:\n")

    message_groups = {}
    for case in successful_cases:
        msg_count = case["message_count"]
        duration = case["duration"]

        if msg_count <= 10:
            group = "1-10条"
        elif msg_count <= 20:
            group = "11-20条"
        elif msg_count <= 50:
            group = "21-50条"
        else:
            group = "50+条"

        if group not in message_groups:
            message_groups[group] = []
        message_groups[group].append(duration)

    for group, times in message_groups.items():
        avg_time = statistics.mean(times)
        output_file.write(f"   {group} 消息: {len(times)} 案例, 平均 {avg_time:.2f}s\n")

    # 详细案例列表
    output_file.write("\n📋 详细案例列表 (按处理时间排序):\n")
    successful_cases.sort(key=lambda x: x["duration"])

    output_file.write(
        f"{'序号':<4} {'案例ID':<12} {'消息数':<6} {'耗时(秒)':<8} {'每消息耗时':<10}\n"
    )
    output_file.write("-" * 50 + "\n")

    for i, case in enumerate(successful_cases, 1):
        time_per_msg = (
            case["duration"] / case["message_count"] if case["message_count"] > 0 else 0
        )
        output_file.write(
            f"{i:<4} {case['case_id']:<12} {case['message_count']:<6} {case['duration']:<8.2f} {time_per_msg:<10.3f}\n"
        )

    # 异常案例分析
    slow_cases = [c for c in successful_cases if c["duration"] > 20]
    if slow_cases:
        output_file.write("\n🐌 慢速案例分析 (>20秒):\n")
        for case in slow_cases:
            time_per_msg = case["duration"] / case["message_count"]
            output_file.write(
                f"   案例 {case['case_id']}: {case['duration']:.2f}s ({case['message_count']} 消息, {time_per_msg:.3f}s/消息)\n"
            )

    fast_cases = [c for c in successful_cases if c["duration"] < 3]
    if fast_cases:
        output_file.write("\n🏃 快速案例分析 (<3秒):\n")
        for case in fast_cases:
            time_per_msg = case["duration"] / case["message_count"]
            output_file.write(
                f"   案例 {case['case_id']}: {case['duration']:.2f}s ({case['message_count']} 消息, {time_per_msg:.3f}s/消息)\n"
            )


def main():
    """主函数."""
    parser = argparse.ArgumentParser(description="时间分布分析")
    parser.add_argument(
        "--engine",
        choices=["basic", "lx"],
        default="basic",
        help="选择执行引擎：basic=BasicPipeline，lx=langextract简化实现",
    )
    args = parser.parse_args()

    print("🚀 开始时间分布分析")
    print(f"🔧 引擎: {args.engine}")

    # 创建输出文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file_path = os.path.join(
        "results", f"time_distribution_analysis_{args.engine}_{timestamp}.txt"
    )
    os.makedirs("results", exist_ok=True)

    with open(output_file_path, "w", encoding="utf-8") as output_file:
        output_file.write("时间分布分析报告\n")
        output_file.write(f"生成时间: {datetime.now().isoformat()}\n")
        output_file.write(f"引擎: {args.engine}\n")
        output_file.write("=" * 60 + "\n")

        # 加载并处理individual cases数据
        cases_data = load_individual_cases()
        if cases_data:
            print(f"📂 处理 individual_cases ({len(cases_data)} 案例)")
            analyze_and_process_dataset(
                cases_data, "individual_cases", output_file, engine=args.engine
            )
        else:
            print("❌ 没有找到可用的案例数据")
            output_file.write("❌ 没有找到可用的案例数据\n")

    print(f"🎉 分析完成! 结果保存到: {output_file_path}")


if __name__ == "__main__":
    main()
