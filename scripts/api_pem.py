import base64
import json
import logging
import uuid
from datetime import datetime

from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA

logger = logging.getLogger(__name__)


def convert_to_pem(key_string):
    """将普通公钥字符串转换为PEM格式."""
    # 移除可能存在的PEM头尾
    key_string = key_string.replace("-----BEGIN PUBLIC KEY-----", "")
    key_string = key_string.replace("-----END PUBLIC KEY-----", "")
    key_string = key_string.strip()

    # 添加PEM格式头尾
    pem_key = "-----BEGIN PUBLIC KEY-----\n"
    # 每64个字符添加一个换行
    for i in range(0, len(key_string), 64):
        pem_key += key_string[i : i + 64] + "\n"
    pem_key += "-----END PUBLIC KEY-----"

    return pem_key


def generate_ms_token(app_id, key_string):
    """生成MS-Token加密认证头."""
    public_key_str = convert_to_pem(key_string)

    try:
        # 1. 生成原始待加密信息
        trace_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        content = {"app_id": app_id, "trace_id": trace_id, "timestamp": timestamp}

        content_str = json.dumps(content, separators=(",", ":"))

        # 2. 使用RSA算法加密
        public_key = RSA.importKey(public_key_str)
        cipher = PKCS1_v1_5.new(public_key)

        # 加密并Base64编码
        encrypted_bytes = cipher.encrypt(content_str.encode("utf-8"))
        ms_token = base64.b64encode(encrypted_bytes).decode("utf-8")

        return ms_token
    except Exception as e:
        logger.error(f"生成MS-Token失败: {str(e)}")
        raise


if __name__ == "__main__":
    app_id = "big-data"
    public_key_str = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCiucD7eIJ8/qqxVUz/N0FSu5YRC/u3OXSjcf84F8SFe23QtZ5sKMpG8ePc7NOZv1t2s9FE2lvSJhkYAvEO5iNwE8yo/dDrCGZX4RZN9O9PXlyPTf3TuXIElTmidR+5Wjz1yEbb7PU5V2Pcrk5G+OKynwSsbGr+YjpQXbrmuthAjQIDAQAB"
    ms_token = generate_ms_token(app_id, public_key_str)
    print(ms_token)
