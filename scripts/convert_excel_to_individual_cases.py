#!/usr/bin/env python3
"""
将 data/original_data.xlsx 转换为 main_pipe.py 可用的 individual_cases JSON 格式。

输出目录默认: data/individual_cases_from_excel/
每个 case 一个 JSON 文件，结构为：
{
  "caseId": "<case_id>",
  "messages": [
    {"id": 1, "type": "USER|AGENT", "msg": "..."},
    ...
  ]
}

注意：每个 case 的消息 id 从 1 开始按时间排序重新编号。

用法：
  uv run python scripts/convert_excel_to_individual_cases.py \
    --input data/original_data.xlsx \
    --out-dir data/individual_cases_from_excel
"""

from __future__ import annotations

import argparse
import json
import os
import re
from dataclasses import dataclass
from datetime import datetime
from html import unescape
from pathlib import Path
from typing import Any

import pandas as pd


HTML_TAG_RE = re.compile(r"<[^>]+>")
WHITESPACE_RE = re.compile(r"\s+")
HYPERLINK_RE = re.compile(r"=HYPERLINK\([^)]*\)")


def strip_html(html_text: str) -> str:
    if not isinstance(html_text, str):
        return ""
    # 先解码实体，再去标签
    text = unescape(html_text)
    text = HTML_TAG_RE.sub(" ", text)
    # 清理 Excel HYPERLINK 伪标签等残留
    text = HYPERLINK_RE.sub(" ", text)
    # 规范空白
    text = WHITESPACE_RE.sub(" ", text).strip()
    return text


def _extract_json_object(s: str) -> str | None:
    """从文本中提取最外层 JSON 对象（介于第一个'{' 和最后一个'}'之间）。失败返回 None。"""
    try:
        first = s.find("{")
        last = s.rfind("}")
        if first == -1 or last == -1 or last <= first:
            return None
        candidate = s[first : last + 1].strip()
        # 简单校验：必须以 { 开头 } 结尾
        if not (candidate.startswith("{") and candidate.endswith("}")):
            return None
        return candidate
    except Exception:
        return None


def _extract_content_from_jsonish(s: str) -> str | None:
    """宽松地从类似 {"content":"...","files":...} 的文本中提取 content 内容。
    适用于外层 JSON 受污染导致无法整体解析的情况。
    """
    try:
        m = re.search(r'"content"\s*:\s*"(.*?)"\s*,\s*"(?:files|file)"', s, flags=re.S)
        if not m:
            return None
        inner = m.group(1)
        # 尝试用 JSON 方式反转义内部字符串
        try:
            return json.loads(f'"{inner}"')
        except Exception:
            return inner
    except Exception:
        return None


def _strip_email_quotes(text: str) -> str:
    """去掉常见的邮件引用段（多语言），保留引用前的最新输入。"""
    if not text:
        return ""
    patterns = [
        r"\bOn .* wrote:\b",  # 英文: On ... wrote:
        r"\bVào .* đã viết:\b",  # 越南语: Vào ... đã viết:
        r"\b于 .* 写道：\b",  # 中文: 于...写道：
    ]
    for pat in patterns:
        m = re.search(pat, text, flags=re.IGNORECASE)
        if m:
            text = text[: m.start()].strip()
            break
    return text


def _is_admin_like_block(text: str, sender: str) -> bool:
    """启发式判断是否为系统/内部记录块，而非自然对话。
    规则：
      - 命中以下审计关键词 >= 2 个即视为内务块；
      - 或者（仅限 AGENT）以 UID:/Email:/Cryptocurrency:/KYC: 等前缀开头。
    """
    if not text:
        return False
    lowered = text.lower()
    audit_keywords = [
        "time",
        "operator",
        "review result",
        "ip address",
        "sumsub",
        "ekyc",
        "supplier information",
        "details",
    ]
    hits = sum(1 for kw in audit_keywords if kw in lowered)
    if hits >= 2:
        return True

    if sender == "AGENT":
        prefixes = ["uid:", "email:", "cryptocurrency:", "kyc:"]
        lstripped = lowered.lstrip()
        if any(lstripped.startswith(pfx) for pfx in prefixes):
            return True
    return False


def _post_clean_text(text: str, sender: str) -> str:
    """统一的消息后处理：去引用、去 HTML 空白、去审计块。"""
    if not text:
        return ""
    text = _strip_email_quotes(text)
    text = strip_html(text)
    if _is_admin_like_block(text, sender):
        return ""
    # 再次规范空白
    text = WHITESPACE_RE.sub(" ", text).strip()
    return text


def parse_hybrid_msg(raw: Any, sender: str) -> str:
    """解析 HYBRID_TEXT：尽力从文本中提取 JSON 并取 content；失败则按 HTML 处理。"""
    if raw is None:
        return ""
    s = str(raw).strip()
    # 先尽量提取 JSON
    candidate = _extract_json_object(s)
    if candidate:
        try:
            obj = json.loads(candidate)
            content = obj.get("content", "")
            # 即使内容清洗后为空，也返回空，避免把 JSON 字符串当成对话文本
            return _post_clean_text(content, sender)
        except Exception:
            pass
    # 若整体解析失败，尝试宽松提取 content
    fallback = _extract_content_from_jsonish(s)
    if fallback is not None:
        return _post_clean_text(fallback, sender)
    # 退回到文本清洗
    return _post_clean_text(s, sender)


def parse_text_msg(raw: Any, sender: str) -> str:
    if raw is None:
        return ""
    s = str(raw).strip()
    # 即使类型标注为 TEXT，也尝试提取潜在的 JSON 块（某些表格会把 HYBRID 混写进 TEXT）
    candidate = _extract_json_object(s)
    if candidate:
        try:
            obj = json.loads(candidate)
            # 仅当存在 content 字段时按 HYBRID 逻辑处理
            if isinstance(obj, dict) and "content" in obj:
                return _post_clean_text(obj.get("content", ""), sender)
        except Exception:
            pass
    return _post_clean_text(s, sender)


SENDER_MAP = {
    "AGENT": "AGENT",
    "USER": "USER",
}


@dataclass
class Message:
    id: int
    type: str
    msg: str

    def to_dict(self) -> dict[str, Any]:
        return {"id": self.id, "type": self.type, "msg": self.msg}


def convert_df_to_cases(df: pd.DataFrame) -> dict[str, list[dict[str, Any]]]:
    """将 DataFrame 转为 {case_id: [messages...]} 结构。"""
    # 统一列名判断
    required_cols = {"f_case_id", "f_sender_type", "f_created_time", "f_type", "f_msg"}
    missing = required_cols - set(df.columns)
    if missing:
        raise ValueError(f"Excel 缺少必要列: {sorted(missing)}")

    # 解析时间列
    df = df.copy()
    df["_created"] = pd.to_datetime(df["f_created_time"], errors="coerce")

    # 按 case 分组
    cases: dict[str, list[dict[str, Any]]] = {}

    for case_id, group in df.groupby("f_case_id"):
        # 排序：先按有效时间，再按原行顺序
        group = group.sort_values(by=["_created"]).reset_index(drop=False)

        messages: list[dict[str, Any]] = []
        next_id = 1
        for _, row in group.iterrows():
            sender_raw = str(row.get("f_sender_type", "")).upper().strip()
            msg_type = SENDER_MAP.get(sender_raw)
            # 仅保留明确的 USER/AGENT，其它 sender_type 直接跳过
            if msg_type not in ("USER", "AGENT"):
                continue

            content_type = str(row.get("f_type", "")).upper().strip()
            raw_msg = row.get("f_msg", "")

            if content_type == "HYBRID_TEXT":
                msg_text = parse_hybrid_msg(raw_msg, msg_type)
            else:  # TEXT 及其他
                msg_text = parse_text_msg(raw_msg, msg_type)

            if not msg_text:
                # 跳过纯空消息
                continue

            messages.append(Message(id=next_id, type=msg_type, msg=msg_text).to_dict())
            next_id += 1

        cases[str(case_id)] = messages

    return cases


def save_cases(cases: dict[str, list[dict[str, Any]]], out_dir: Path) -> list[Path]:
    out_dir.mkdir(parents=True, exist_ok=True)
    written: list[Path] = []
    for case_id, messages in cases.items():
        data = {"caseId": str(case_id), "messages": messages}
        out_path = out_dir / f"case_{case_id}.json"
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        written.append(out_path)
    return written


def main():
    parser = argparse.ArgumentParser(
        description="Convert original_data.xlsx to individual_cases JSON format"
    )
    parser.add_argument(
        "--input", default="data/original_data_v2.xlsx", help="输入 Excel 文件路径"
    )
    parser.add_argument(
        "--out-dir",
        default="data/individual_cases_from_excel",
        help="输出目录（不会覆盖已有 individual_cases）",
    )
    args = parser.parse_args()

    in_path = Path(args.input)
    out_dir = Path(args.out_dir)

    if not in_path.exists():
        raise SystemExit(f"❌ 输入文件不存在: {in_path}")

    print(f"📄 读取 Excel: {in_path}")
    df = pd.read_excel(in_path)
    print(f"✅ 读取完成：{len(df)} 行")

    print("🔄 正在转换为 cases/messages...")
    cases = convert_df_to_cases(df)
    print(f"✅ 转换完成：{len(cases)} 个 case")

    print(f"💾 写入输出目录: {out_dir}")
    written = save_cases(cases, out_dir)
    print(f"🎉 已写入 {len(written)} 个 JSON 文件")

    # 提示如何用 main_pipe.py
    example_case = next(iter(cases.keys()), None)
    if example_case:
        print("\n📌 示例 (Python)：加载一个 case 后传给 BasicPipeline.run")
        print("-" * 60)
        demo = {
            "caseId": example_case,
            "messages": cases[example_case][:3],  # 只展示前 3 条
        }
        print(json.dumps(demo, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()
