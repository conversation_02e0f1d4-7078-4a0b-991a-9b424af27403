import json
import os

from dc_ai_red_line_review.main_pipe import BasicPipeline

# case_id = "10203334"
case_id = "10089502"

base_dir = os.path.join("data", "individual_cases")
file_path = os.path.join(base_dir, f"case_{case_id}.json")


with open(file_path, encoding="utf-8") as f:
    data = json.load(f)
    messages = data.get("messages")

    main_pipe = BasicPipeline()
    result = main_pipe.run(messages=messages, caseId=case_id)
    print("Result:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
