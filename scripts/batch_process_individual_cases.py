#!/usr/bin/env python3
"""批量处理individual_cases目录下的案例文件
按照demo.py的方式处理每个案例，并将结果保存为CSV格式
"""

import csv
import json
import argparse
import html

# import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any

# from pprint import pprint
# import asyncio
from dc_ai_red_line_review import BasicPipeline


def load_case_file(file_path: str) -> dict[str, Any]:
    """加载单个案例文件"""
    try:
        with open(file_path, encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载文件失败 {file_path}: {e}")
        return None


def process_single_case(
    case_data: dict[str, Any], pipeline: BasicPipeline
) -> dict[str, Any]:
    """处理单个案例"""
    case_id = case_data.get("caseId", "unknown")
    messages = case_data.get("messages", [])

    try:
        print(f"🔄 正在处理案例: {case_id}")
        print(f"   消息数量: {len(messages)}")

        # 运行管道处理
        result = pipeline.run(messages=messages, caseId=case_id)
        # result =  asyncio.run(pipeline.run(messages=messages, caseId=case_id))

        print(f"✅ 案例 {case_id} 处理完成")
        return {
            "case_id": case_id,
            "status": "success",
            "result": result,
            "message_count": len(messages),
            "processed_at": datetime.now().isoformat(),
            # 为命中明细表携带原始消息
            "messages": messages,
        }

    except Exception as e:
        print(f"❌ 案例 {case_id} 处理失败: {e}")
        return {
            "case_id": case_id,
            "status": "error",
            "error": str(e),
            "message_count": len(messages),
            "processed_at": datetime.now().isoformat(),
        }


def convert_result_to_json_string(result: dict[str, Any]) -> str:
    """将处理结果转换为JSON字符串"""
    try:
        return json.dumps(result, ensure_ascii=False, separators=(",", ":"))
    except Exception as e:
        return f"JSON转换错误: {str(e)}"


def generate_result_markdown(result: dict[str, Any]) -> str:
    """将 result 转为精简版 Markdown 表格（仅表格，无头部信息）。

    预期 result 结构：
    {
        "id": str,
        "review_res": {
            "key_contact": {"hit_rule": bool, "values": [str], "matched_ids": [[int]]},
            "internal_system": { ... 同上 ... },
            "government_inquiry": [{"type": str, "hit_rule": bool, "values": [str], "matched_ids": [[int]]}, ...],
            "sensitive_inquiry":  [同上],
            "sensitive_reply":    [同上],
        }
    }
    """
    try:
        review_res = (result or {}).get("review_res", {})

        # 工具函数
        def dict_hit_and_count(d: dict) -> tuple[bool, int, str]:
            """返回 (是否命中, 证据条数, 关键词拼接字符串)。"""
            if not isinstance(d, dict):
                return False, 0, "-"
            values = d.get("values", []) or []
            hit = bool(d.get("hit_rule")) and len(values) > 0
            # 证据条数：将 matched_ids 展平后的数量
            matched_ids = d.get("matched_ids", []) or []
            count = sum(len(x) for x in matched_ids if isinstance(x, list))
            keywords = "、".join(values) if values else "-"
            return hit, count, keywords if keywords else "-"

        def list_hit_and_count(items: list) -> tuple[bool, int, str]:
            """针对 list[dict] 的类别，返回 (是否命中, 证据条数, 命中类型合并字符串)。"""
            if not isinstance(items, list):
                return False, 0, "-"

            def item_is_hit(it: dict) -> bool:
                return bool(it.get("hit_rule")) and len(it.get("values", []) or []) > 0

            hit_items = [it for it in items if isinstance(it, dict) and item_is_hit(it)]
            hit = len(hit_items) > 0
            # 证据条数：仅统计命中项的 matched_ids
            count = 0
            types: list[str] = []
            for it in hit_items:
                matched_ids = it.get("matched_ids", []) or []
                count += sum(len(x) for x in matched_ids if isinstance(x, list))
                t = it.get("type")
                if isinstance(t, str) and t:
                    types.append(t)
            # 按出现顺序去重
            seen = set()
            ordered_types = []
            for t in types:
                if t not in seen:
                    seen.add(t)
                    ordered_types.append(t)
            types_str = "、".join(ordered_types) if ordered_types else "-"
            return hit, count, types_str

        # 各维度数据
        kc_hit, kc_cnt, kc_kw = dict_hit_and_count(review_res.get("key_contact", {}))
        is_hit_str = lambda x: "✅" if x else "❌"

        si_hit, si_cnt, si_types = list_hit_and_count(
            review_res.get("sensitive_inquiry", [])
        )
        gi_hit, gi_cnt, gi_types = list_hit_and_count(
            review_res.get("government_inquiry", [])
        )
        is_hit_str = lambda x: "✅" if x else "❌"

        is_hit_str = lambda x: "✅" if x else "❌"
        insys_hit, insys_cnt, insys_kw = dict_hit_and_count(
            review_res.get("internal_system", {})
        )
        sr_hit, sr_cnt, sr_types = list_hit_and_count(
            review_res.get("sensitive_reply", [])
        )

        # 组装 Markdown 表格
        lines = []
        lines.append("| 规则 | 命中 | 关键词/类型 | 证据条数 |")
        lines.append("| --- | --- | --- | --- |")
        lines.append(
            f"| 关键联系方式 | {is_hit_str(kc_hit)} | {kc_kw or '-'} | {kc_cnt} |"
        )
        lines.append(
            f"| 敏感询问 | {is_hit_str(si_hit)} | {si_types or '-'} | {si_cnt} |"
        )
        lines.append(
            f"| 政府询问 | {is_hit_str(gi_hit)} | {gi_types or '-'} | {gi_cnt} |"
        )
        lines.append(
            f"| 内部系统 | {is_hit_str(insys_hit)} | {insys_kw or '-'} | {insys_cnt} |"
        )
        lines.append(
            f"| 敏感回复 | {is_hit_str(sr_hit)} | {sr_types or '-'} | {sr_cnt} |"
        )

        return "\n".join(lines)
    except Exception as e:
        return f"Markdown 生成错误: {e}"


def generate_hits_rows(
    case_result: dict[str, Any], messages: list[dict[str, Any]]
) -> list[dict[str, Any]]:
    """按照方案A，基于单个案例的 result 与原始 messages 生成命中明细的行列表。

    输出列：工单号、命中关键词、关键词所在整条段落、命中规则、人工标记
    行粒度：按“消息+规则”聚合，每行代表某工单的某条消息在某规则下的命中。
    """
    rows: list[dict[str, Any]] = []
    if not isinstance(case_result, dict):
        return rows

    case_id = case_result.get("id", "")
    review_res = case_result.get("review_res", {}) or {}

    # 消息ID -> 文本
    id_to_text: dict[int, str] = {}
    try:
        for m in messages or []:
            mid = m.get("id")
            msg = m.get("msg", "")
            if isinstance(mid, int):
                # HTML 实体反转义，NBSP 转空格；去除内部换行，避免 CSV 断行
                id_to_text[mid] = html.unescape(str(msg)).replace("\xa0", " ").replace("\r\n", " ").replace("\n", " ")
    except Exception:
        pass

    def add_rows_for_dict_category(cat_key: str, rule_label: str):
        item = review_res.get(cat_key, {}) or {}
        if not isinstance(item, dict):
            return
        values = item.get("values", []) or []
        matched_ids = item.get("matched_ids", []) or []
        # 聚合到 message_id 维度
        msg_to_values: dict[int, list[str]] = {}
        for v, ids in zip(values, matched_ids):
            if not isinstance(ids, list):
                continue
            for mid in ids:
                if isinstance(mid, int) and mid in id_to_text:
                    msg_to_values.setdefault(mid, [])
                    if v not in msg_to_values[mid]:
                        msg_to_values[mid].append(v)
        for mid, vs in msg_to_values.items():
            rows.append(
                {
                    "工单号": case_id,
                    "命中关键词": "、".join([html.unescape(v).replace("\xa0", " ") for v in vs]) if vs else "",
                    "关键词所在整条段落": id_to_text.get(mid, ""),
                    "命中规则": rule_label,
                    "人工标记": "",
                }
            )

    def add_rows_for_list_category(cat_key: str):
        items = review_res.get(cat_key, []) or []
        if not isinstance(items, list):
            return
        # msg_id + rule_label 聚合
        group: dict[tuple[int, str], list[str]] = {}
        for it in items:
            if not isinstance(it, dict):
                continue
            # 仅统计真正命中的项
            vals = it.get("values", []) or []
            matched_ids = it.get("matched_ids", []) or []
            hit = bool(it.get("hit_rule")) and len(vals) > 0
            rule_label = it.get("type") or ""
            if not hit or not rule_label:
                continue
            for v, ids in zip(vals, matched_ids):
                if not isinstance(ids, list):
                    continue
                for mid in ids:
                    if isinstance(mid, int) and mid in id_to_text:
                        key = (mid, rule_label)
                        group.setdefault(key, [])
                        if v not in group[key]:
                            group[key].append(v)
        for (mid, rule_label), vs in group.items():
            rows.append(
                {
                    "工单号": case_id,
                    "命中关键词": "、".join([html.unescape(v).replace("\xa0", " ") for v in vs]) if vs else "",
                    "关键词所在整条段落": id_to_text.get(mid, ""),
                    "命中规则": rule_label,
                    "人工标记": "",
                }
            )

    # 字典类：关键联系方式、内部系统
    add_rows_for_dict_category("key_contact", "关键联系方式")
    add_rows_for_dict_category("internal_system", "内部系统")

    # 列表类：敏感询问、政府询问、敏感回复（命中规则来自各 item 的 type 字段）
    add_rows_for_list_category("sensitive_inquiry")
    add_rows_for_list_category("government_inquiry")
    add_rows_for_list_category("sensitive_reply")

    # 若无命中，则也输出一行占位，保证“每个工单至少一行”
    if not rows:
        rows.append(
            {
                "工单号": case_id,
                "命中关键词": "",
                "关键词所在整条段落": "",
                "命中规则": "",
                "人工标记": "",
            }
        )
    return rows


def save_hits_to_csv(results: list[dict[str, Any]], output_file: str):
    """将所有案例的命中明细保存为 CSV（方案A：按消息+规则聚合）。

    要求：包含所有工单。若未命中或出错，也输出一行，仅填“工单号”，其它列留空。
    """
    fieldnames = ["工单号", "命中关键词", "关键词所在整条段落", "命中规则", "人工标记"]
    rows: list[dict[str, Any]] = []
    for r in results or []:
        case_id = r.get("case_id", "")
        status = r.get("status")
        if status == "success" and isinstance(r.get("result"), dict) and isinstance(r.get("messages"), list):
            case_rows = generate_hits_rows(r["result"], r["messages"])  # 至少一行
            rows.extend(case_rows)
        else:
            # 出错或结构异常：也输出占位行
            rows.append(
                {
                    "工单号": case_id,
                    "命中关键词": "",
                    "关键词所在整条段落": "",
                    "命中规则": "",
                    "人工标记": "",
                }
            )

    with open(output_file, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(rows)

    print(f"📑 命中明细已保存到: {output_file}")
    print(f"   明细行数: {len(rows)}")


def save_results_to_csv(results: list[dict[str, Any]], output_file: str):
    """将结果保存为CSV格式 - 简化版本，直接保存JSON字符串"""
    if not results:
        print("⚠️ 没有结果可保存")
        return

    # 定义CSV的字段（新增 result_markdown 列）
    fieldnames = [
        "case_id",
        "status",
        "message_count",
        "processed_at",
        "result_json",
        "result_markdown",
        "error",
    ]

    csv_rows = []
    for result in results:
        row = {
            "case_id": result["case_id"],
            "status": result["status"],
            "message_count": result.get("message_count", 0),
            "processed_at": result["processed_at"],
            "result_json": "",
            "result_markdown": "",
            "error": "",
        }

        if result["status"] == "success" and "result" in result:
            # 直接将整个result转换为JSON字符串保存到result_json列
            row["result_json"] = convert_result_to_json_string(result["result"])
            # 生成精简版 Markdown 表格
            row["result_markdown"] = generate_result_markdown(result["result"])
        elif result["status"] == "error":
            row["error"] = result.get("error", "")

        csv_rows.append(row)

    # 写入CSV文件
    with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_rows)

    print(f"📊 结果已保存到: {output_file}")
    print(f"   总记录数: {len(csv_rows)}")
    print(f"   字段数: {len(fieldnames)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量处理 individual_cases")
    parser.add_argument(
        "--limit", type=int, default=0, help="仅处理前 N 个案例，0 表示不限制"
    )
    args = parser.parse_args()

    print("🚀 开始批量处理 individual_cases 目录")
    print("=" * 60)
    if args.limit and args.limit > 0:
        print(f"本次仅处理前 {args.limit} 个案例用于预览效果")

    # 设置路径
    project_root = Path(__file__).parent.parent
    cases_dir = project_root / "data" / "individual_cases"
    results_dir = project_root / "results"

    # 确保结果目录存在
    results_dir.mkdir(exist_ok=True)

    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # 不再生成 json 文件
    csv_output_file = results_dir / f"individual_cases_results_{timestamp}.csv"
    hits_output_file = results_dir / f"individual_cases_hits_{timestamp}.csv"

    # 检查输入目录
    if not cases_dir.exists():
        print(f"❌ 输入目录不存在: {cases_dir}")
        return

    # 获取所有JSON文件
    json_files = list(cases_dir.glob("*.json"))
    if not json_files:
        print(f"❌ 在 {cases_dir} 中没有找到JSON文件")
        return

    # 按文件名排序，便于稳定复现
    json_files = sorted(json_files, key=lambda p: p.name)

    # 限制数量
    if args.limit and args.limit > 0:
        json_files = json_files[: args.limit]

    print(f"📁 本次将处理 {len(json_files)} 个案例文件")

    # 初始化管道
    print("🔧 初始化处理管道...")
    pipeline = BasicPipeline()

    # 处理所有案例
    results = []
    start_time = time.time()

    try:
        for i, json_file in enumerate(json_files, 1):
            print(f"\n📋 进度: {i}/{len(json_files)} - {json_file.name}")

            # 加载案例数据
            case_data = load_case_file(str(json_file))
            if case_data is None:
                results.append(
                    {
                        "case_id": json_file.stem,
                        "status": "error",
                        "error": "Failed to load file",
                        "message_count": 0,
                        "processed_at": datetime.now().isoformat(),
                    }
                )
                continue

            # 处理单个案例
            result = process_single_case(case_data, pipeline)
            results.append(result)

            # 可选: 添加延迟以避免过载
            # time.sleep(0.1)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")

    # 计算统计信息
    end_time = time.time()
    total_time = end_time - start_time
    successful_cases = sum(1 for r in results if r["status"] == "success")
    failed_cases = len(results) - successful_cases

    print("\n📊 处理完成统计:")
    print(f"   总处理时间: {total_time:.2f} 秒")
    print(f"   成功案例: {successful_cases}")
    print(f"   失败案例: {failed_cases}")
    print(f"   平均处理时间: {total_time / len(results):.2f} 秒/案例")

    # 保存结果
    print("\n💾 保存结果...")

    # 不再保存 JSON 格式，保留结果汇总 CSV
    save_results_to_csv(results, str(csv_output_file))
    print(f"� 汇总结果已保存到: {csv_output_file}")

    # 另存命中明细 CSV（方案A）
    save_hits_to_csv(results, str(hits_output_file))

    print("\n🎉 批量处理完成!")


if __name__ == "__main__":
    main()
