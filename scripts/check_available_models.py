#!/usr/bin/env python3
"""检查litellm部署中可用的模型列表."""
import os

import httpx
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv(override=True)

def check_models_via_openai_client():
    """使用OpenAI客户端检查模型列表"""
    print("=" * 60)
    print("🔍 使用OpenAI客户端检查模型列表")
    print("=" * 60)
    
    try:
        client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=30.0),
        )
        
        print(f"📡 连接到: {os.environ['QWQ_BASE_URL']}")
        print(f"🔑 API Key: {os.environ['QWQ_API_KEY'][:20]}...")
        print()
        
        # 获取模型列表
        models = client.models.list()
        
        print(f"✅ 找到 {len(models.data)} 个可用模型:")
        print("-" * 40)
        
        for i, model in enumerate(models.data, 1):
            print(f"{i:2d}. {model.id}")
            if hasattr(model, "created"):
                print(f"    创建时间: {model.created}")
            if hasattr(model, "owned_by"):
                print(f"    拥有者: {model.owned_by}")
            print()
            
    except Exception as e:
        print(f"❌ OpenAI客户端检查失败: {e}")
        return None

def check_models_via_curl():
    """使用HTTP请求直接检查模型列表"""
    print("=" * 60)
    print("🌐 使用HTTP请求检查模型列表")
    print("=" * 60)
    
    try:
        url = f"{os.environ['QWQ_BASE_URL']}/v1/models"
        headers = {
            "Authorization": f"Bearer {os.environ['QWQ_API_KEY']}",
            "Content-Type": "application/json"
        }
        
        print(f"📡 请求URL: {url}")
        print()
        
        with httpx.Client(verify=False, timeout=30.0) as client:
            response = client.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                models = data.get("data", [])
                
                print(f"✅ HTTP请求成功，找到 {len(models)} 个模型:")
                print("-" * 40)
                
                for i, model in enumerate(models, 1):
                    model_id = model.get("id", "Unknown")
                    print(f"{i:2d}. {model_id}")
                    
                    # 显示其他可用信息
                    for key, value in model.items():
                        if key != "id":
                            print(f"    {key}: {value}")
                    print()
                    
                return models
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ HTTP请求检查失败: {e}")
        return None

def test_current_model():
    """测试当前配置的模型是否可用"""
    print("=" * 60)
    print("🧪 测试当前配置的模型")
    print("=" * 60)
    
    current_model = os.environ.get("MODEL_NAME", "Unknown")
    print(f"🎯 当前配置模型: {current_model}")
    
    try:
        client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=30.0),
        )
        
        # 发送简单测试请求
        response = client.chat.completions.create(
            model=current_model,
            messages=[
                {"role": "user", "content": "Hello, please respond with 'Model is working'"}
            ],
            max_tokens=10,
            temperature=0
        )
        
        if response.choices and response.choices[0].message:
            print(f"✅ 模型 {current_model} 工作正常")
            print(f"📝 响应: {response.choices[0].message.content}")
            print(f"📊 Token使用: {response.usage.prompt_tokens} prompt + {response.usage.completion_tokens} completion = {response.usage.total_tokens} total")
        else:
            print(f"⚠️ 模型 {current_model} 响应异常")
            
    except Exception as e:
        print(f"❌ 模型 {current_model} 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 LiteLLM 模型检查工具")
    print(f"⏰ 当前时间: {os.popen('date').read().strip()}")
    print()
    
    # 检查环境变量
    required_vars = ["QWQ_BASE_URL", "QWQ_API_KEY", "MODEL_NAME"]
    missing_vars = [var for var in required_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {missing_vars}")
        print("请确保 .env 文件配置正确")
        return
    
    # 显示当前配置
    print("📋 当前配置:")
    print(f"   Base URL: {os.environ['QWQ_BASE_URL']}")
    print(f"   API Key: {os.environ['QWQ_API_KEY'][:20]}...")
    print(f"   Model: {os.environ['MODEL_NAME']}")
    print()
    
    # 执行检查
    check_models_via_openai_client()
    print()
    
    check_models_via_curl()
    print()
    
    test_current_model()

if __name__ == "__main__":
    main()
