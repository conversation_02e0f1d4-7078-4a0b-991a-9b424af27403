# DC AI Customer Service Red Line Review

一个基于AI的客服对话红线审查系统，用于自动检测和分析客服对话中的敏感内容、不当言论和合规风险。

## 🚀 功能特性

- **多维度审查**: 支持关键联系方式、敏感询问、政府询问、内部系统信息、敏感回复等多个维度的内容审查
- **智能分块处理**: 自动处理长对话内容，支持大规模对话数据的分析
- **并发处理**: 使用多线程并发执行多个审查任务，提高处理效率
- **结果去重**: 智能合并和去重分析结果，避免重复内容
- **消息匹配**: 自动匹配敏感内容与具体消息ID，便于定位问题
- **灵活配置**: 支持本地和远程配置文件，适应不同部署环境

## 📦 安装

### 使用 uv (推荐)

```bash
# 安装 uv (如果尚未安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装项目依赖
uv sync
```

### 使用 pip

```bash
pip install dc_ai_red_line_review
```

### 从源码安装

```bash
git clone <repository-url>
cd dc-ai-customer-service
uv sync
```

## 🔧 配置

### 环境变量

创建 `.env` 文件并配置以下环境变量：

```env
# AI模型配置
QWQ_BASE_URL=your_model_base_url
QWQ_API_KEY=your_api_key

# 提示词配置
PROMPT_SOURCE=local  # 或 remote
PROMPT_PATH=./configuration  # 本地配置文件路径
# 或者使用远程配置
# PROMPT_DICT={"key_contact": [...], "sensitive_inquiry": [...]}
```

### 配置文件

在 `configuration/prompt.json` 中配置审查规则：

```json
{
  "key_contact": ["电话", "微信", "QQ", "邮箱"],
  "sensitive_inquiry": "检测用户是否询问敏感信息的提示词",
  "government_inquiry": "检测政府相关询问的提示词",
  "internal_system": ["内部系统", "后台", "数据库"],
  "sensitive_reply": "检测客服敏感回复的提示词"
}
```

## 🚀 快速开始

### 基本使用

```python
from dc_ai_red_line_review import BasicPipeline

# 初始化管道
pipeline = BasicPipeline()

# 准备消息数据
messages = [
    {"id": 1, "type": "USER", "msg": "我忘记密码了"},
    {"id": 2, "type": "AGENT", "msg": "我来帮您处理"},
    {"id": 3, "type": "AGENT", "msg": "您可以联系我的微信：123456"}
]

# 执行审查
result = pipeline.run(messages=messages, caseId="case_001")
print(result)
```

### 输出格式

```python
{
    "id": "case_001",
    "review_res": {
        "key_contact": {
            "hit_rule": True,
            "values": ["微信"],
            "matched_ids": [[3]]
        },
        "sensitive_inquiry": {...},
        "government_inquiry": {...},
        "internal_system": {...},
        "sensitive_reply": {...}
    }
}
```

## 📋 API 参考

### BasicPipeline

主要的处理管道类。

#### 方法

##### `run(messages, caseId="")`

执行完整的红线审查流程。

**参数:**
- `messages` (list): 消息列表，每个消息包含 `id`, `type`, `msg` 字段
- `caseId` (str): 案例ID，用于日志和结果标识

**返回:**
- `dict`: 包含案例ID和审查结果的字典

##### `run_sync(content, content_tokens=None)`

同步执行所有审查任务。

**参数:**
- `content` (str): 要审查的内容
- `content_tokens` (int, optional): 内容的token数量

**返回:**
- `dict`: 各个维度的审查结果

## 🔍 审查维度

### 1. 关键联系方式 (key_contact)
检测对话中是否包含电话、微信、QQ等联系方式信息。

### 2. 敏感询问 (sensitive_inquiry)
使用AI模型检测用户是否询问敏感或不当信息。

### 3. 政府询问 (government_inquiry)
检测是否涉及政府、监管机构相关的询问。

### 4. 内部系统 (internal_system)
检测是否泄露内部系统、后台等敏感信息。

### 5. 敏感回复 (sensitive_reply)
检测客服回复是否包含不当言论或敏感内容。

## 🛠️ 开发

### 项目结构

```
dc-ai-customer-service/
├── dc_ai_red_line_review/          # 主要代码包
│   ├── __init__.py
│   ├── main_pipe.py               # 主处理管道
│   ├── core.py                    # 核心组件
│   └── utils.py                   # 工具函数
├── configuration/                  # 配置文件
│   └── prompt.json
├── scripts/                       # 示例脚本
│   ├── demo.py                    # 基本示例
│   └── model_test.py              # 模型测试
├── logs/                          # 日志文件
├── pyproject.toml                 # 项目配置
└── README.md
```

### 运行测试

```bash
# 运行基本示例
uv run scripts/demo.py

# 运行模型测试
uv run scripts/model_test.py
```

### 代码格式化

项目使用 Ruff 进行代码格式化和检查：

```bash
# 格式化代码
uv run ruff format

# 检查代码
uv run ruff check
```

## 📊 性能特性

- **智能分块**: 自动将长对话分割为8000 token的块，支持1000 token重叠
- **并发处理**: 使用ThreadPoolExecutor并发执行5个审查任务
- **内存优化**: 高效的token计算和内容处理
- **结果合并**: 智能合并多个分块的审查结果

## 🔧 配置选项

### Token 配置

```python
CHUNK_SIZE_TOKENS = 8_000    # 每个分块的最大token数
OVERLAP_TOKENS = 1_000       # 分块间重叠的token数
```

### 日志配置

系统使用结构化日志记录，支持详细的处理过程追踪。


## 👥 支持

- Adofe Zhu


**注意**: 本系统用于客服质量监控和合规审查，请确保在使用时遵守相关法律法规和隐私保护要求。