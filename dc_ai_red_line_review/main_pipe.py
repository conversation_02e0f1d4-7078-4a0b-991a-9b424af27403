import hashlib
import json
import os
import time
from concurrent.futures import Thr<PERSON>PoolExecutor, as_completed

import httpx
from dotenv import load_dotenv
from openai import OpenAI
from pydantic import BaseModel, ValidationError

from dc_ai_red_line_review.core import CoreComponents
from dc_ai_red_line_review.retrieval_core import Retrieval<PERSON>ore
from dc_ai_red_line_review.utils import (
    get_logger,
    get_token_count,
    timing_decorator,
)

# timing_decorator is already imported from utils at top


load_dotenv(override=True)


# 统一的token限制配置
CHUNK_SIZE_TOKENS = 8_000
OVERLAP_TOKENS = 1_000


# Pydantic model for message validation
class MessageItem(BaseModel):
    id: int  # change from str to int to accept numeric IDs
    type: str
    msg: str


class BasicPipeline:
    def __init__(self):
        self.logger = get_logger(module_name="red_line_review")

        if os.environ.get("PROMPT_SOURCE") == "local":
            with open(os.path.join(os.environ["PROMPT_PATH"], "prompt.json")) as file:
                self.prompt_dict = json.load(file)
        else:
            self.prompt_dict = json.loads(os.environ["PROMPT_DICT"])

        self.logger.info("Initializing pipeline components")

        self.model_client = OpenAI(
            base_url=os.environ["QWQ_BASE_URL"],
            api_key=os.environ["QWQ_API_KEY"],
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

        self.core_components = CoreComponents(
            model_client=self.model_client, prompt_dict=self.prompt_dict
        )

        # Initialize RetrievalCore for advanced chunking and vector storage
        self.retrieval_core = RetrievalCore(
            split_length=CHUNK_SIZE_TOKENS,
            split_overlap=OVERLAP_TOKENS,
        )
        # Per-instance cache for document review results to avoid duplicate LLM calls across categories
        self._doc_review_cache: dict[str, dict] = {}
        self.logger.info("Initialized RetrievalCore for vector storage")

    def _load_query_config(self) -> dict:
        """从配置文件加载检索query配置.

        Returns:
            dict: 类别查询配置字典
        """
        try:
            config_path = os.path.join(os.path.dirname(__file__), "query_config.json")
            with open(config_path, encoding="utf-8") as f:
                config = json.load(f)

            category_queries = config.get("category_queries", {})
            self.logger.info(
                f"Loaded query config for {len(category_queries)} categories"
            )

            # 打印每个类别的query数量
            for category, queries in category_queries.items():
                self.logger.debug(f"Category {category}: {len(queries)} queries")

            return category_queries

        except FileNotFoundError:
            self.logger.error("Query config file not found, using empty config")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse query config file: {e}")
            return {}
        except Exception as e:
            self.logger.error(f"Error loading query config: {e}")
            return {}

    def _get_detailed_instruct(self, task_description: str, query: str) -> str:
        """为query添加任务描述指令.

        Args:
            task_description: 任务描述
            query: 原始查询

        Returns:
            str: 带指令的查询
        """
        return f"Instruct: {task_description}\nQuery: {query}"

    def _get_category_task_description(self, category: str) -> str:
        """根据类别获取对应的任务描述.

        Args:
            category: 类别名称

        Returns:
            str: 任务描述
        """
        task_descriptions = {
            "consulting_company_info": "Given a customer service conversation, retrieve passages that contain inquiries about company information, business details, or corporate background",
            "selling_user_info": "Given a customer service conversation, retrieve passages that contain discussions about selling user information, personal data, or privacy violations",
            "negative_news": "Given a customer service conversation, retrieve passages that contain mentions of negative news, scandals, or bad reputation about the company",
            "major_complaints": "Given a customer service conversation, retrieve passages that contain major complaints, serious issues, or customer dissatisfaction",
            "request_contact_information": "Given a customer service conversation, retrieve passages that contain requests for contact information or communication details",
            "spam_messages": "Given a customer service conversation, retrieve passages that contain spam messages, abusive content, or harassment",
        }

        return task_descriptions.get(
            category,
            "Given a customer service conversation, retrieve relevant passages that match the query",
        )

    def _compute_char_limit_by_primary_language(
        self, messages: list[dict]
    ) -> tuple[str, int, float]:
        """Estimate primary language from messages and derive char-based chunk size.

        - Primary language is decided by counting characters by script:
          zh: U+4E00–U+9FFF, ru: U+0400–U+04FF, en: [A-Za-z]
        - Char limit = target_tokens (CHUNK_SIZE_TOKENS) * language_coefficient
        - Returns (primary_lang, char_limit_chars, chars_per_token_coef)
        """
        zh_count = 0
        en_count = 0
        ru_count = 0

        for msg in messages:
            text = msg.get("msg", "") or ""
            for ch in text:
                # Chinese Han
                if "\u4e00" <= ch <= "\u9fff":
                    zh_count += 1
                # Cyrillic
                elif "\u0400" <= ch <= "\u04ff":
                    ru_count += 1
                # Latin letters (basic A-Z a-z)
                elif ("A" <= ch <= "Z") or ("a" <= ch <= "z"):
                    en_count += 1

        # Determine primary language
        counts = {"zh": zh_count, "en": en_count, "ru": ru_count}
        primary_lang = max(counts, key=counts.get) if any(counts.values()) else "en"

        # Language coefficients: approximate chars per token
        lang_coef = {"zh": 1.1, "en": 4.0, "ru": 1.9}
        coef = lang_coef.get(primary_lang, 2.0)

        # Char limit derived directly from target tokens * coefficient
        char_limit = int(CHUNK_SIZE_TOKENS * coef)

        self.logger.info(
            f"Primary language detected: {primary_lang} | zh={zh_count}, en={en_count}, ru={ru_count} | "
            f"char_limit={char_limit} (target_tokens={CHUNK_SIZE_TOKENS}, coef={coef})"
        )
        return primary_lang, char_limit, coef

    @timing_decorator
    def _split_messages_using_retrieval_core(
        self, messages: list, case_id: str
    ) -> tuple[list, int]:
        """使用RetrievalCore进行分块处理.

        Args:
            messages: 消息列表
            case_id: 案例ID

        Returns:
            tuple: (分块文档列表, 分块数量)
        """
        try:
            self.logger.info(
                f"Using RetrievalCore for chunking {len(messages)} messages"
            )
            # 根据主语种动态计算字符分块上限，并在本次运行中使用
            _, char_limit, coef = self._compute_char_limit_by_primary_language(messages)

            # 使用RetrievalCore的分块方法（动态分块参数，不重建底层连接）
            chunk_docs = self.retrieval_core._get_splitter_res(
                case_id,
                messages,
                split_length=char_limit,
                split_overlap=int(OVERLAP_TOKENS * coef),
            )
            chunk_count = len(chunk_docs)

            self.logger.info(f"RetrievalCore chunking result: {chunk_count} chunks")

            # 打印每个分块的详细信息
            for i, doc in enumerate(chunk_docs):
                content_length = len(doc.content)
                estimated_tokens = get_token_count(doc.content)
                self.logger.info(
                    f"  Chunk {i + 1}: {content_length:,} chars, ~{estimated_tokens:,} tokens"
                )

            return chunk_docs, chunk_count

        except Exception as e:
            self.logger.error(f"RetrievalCore chunking failed: {e}")
            raise ValueError(f"RetrievalCore chunking failed: {e}")

    @timing_decorator
    def _process_large_chunk_count_with_vector_storage(
        self, chunk_docs: list, case_id: str
    ) -> dict:
        """处理大量分块的情况，先插入向量引擎然后进行后续处理.

        Args:
            chunk_docs: 分块文档列表
            case_id: 案例ID

        Returns:
            dict: 处理结果
        """
        self.logger.info(
            f"Processing {len(chunk_docs)} chunks with vector storage for case {case_id}"
        )

        try:
            # 将所有分块插入到向量引擎
            self.logger.info("Inserting chunks into vector storage...")
            self.retrieval_core.insert_documents(chunk_docs)
            self.logger.info("Successfully inserted all chunks into vector storage")

            result = self._large_chunk_processing(chunk_docs, case_id)

            # 可选：处理后按 caseId 清理，避免索引膨胀（如果需要长期保留则跳过）
            try:
                self.retrieval_core.delete_documents_by_case(case_id)
                self.logger.info(f"Cleaned up vector storage for caseId={case_id}")
            except Exception as cleanup_err:
                self.logger.warning(
                    f"Cleanup for caseId={case_id} failed: {cleanup_err}"
                )

            return result

        except Exception as e:
            self.logger.error(f"Vector storage processing failed: {e}")
            raise ValueError(f"Vector storage processing failed: {e}")

    @timing_decorator
    def _large_chunk_processing(self, chunk_docs: list, case_id: str) -> dict:
        """处理大量分块的核心逻辑：检索、审查和合并."""
        self.logger.info(
            f"Starting multi-query retrieval processing for {len(chunk_docs)} chunks"
        )

        # 1. 加载各类别的检索查询语句
        category_queries = self._load_query_config()

        # 2. 为每个类别执行检索，并获取去重后的相关文档
        category_contents = self._retrieve_and_deduplicate(category_queries, case_id)

        # 3. 对检索到的唯一文档进行审查
        results = self._review_retrieved_documents(category_contents)

        # 4. 添加摘要类别 (sensitive_inquiry, sensitive_reply)
        self._add_summary_categories(results)

        self.logger.info("Multi-query retrieval processing completed")
        return results

    @timing_decorator
    def _retrieve_and_deduplicate(self, category_queries: dict, case_id: str) -> dict:
        """为每个类别执行检索，并返回一个包含去重后文档的字典.

        Args:
            category_queries: 从配置文件加载的类别查询。
            case_id: 当前案例的ID，用于在向量存储中过滤。

        Returns:
            一个字典，键是类别名称，值是该类别下唯一的文档列表。
        """
        category_contents = {}
        for category, queries in category_queries.items():
            self.logger.info(
                f"Processing retrieval for category: {category} with {len(queries)} queries"
            )
            category_doc_ids = set()
            category_documents = []
            task_description = self._get_category_task_description(category)

            with ThreadPoolExecutor(max_workers=3) as executor:
                future_to_query = {
                    executor.submit(
                        self.retrieval_core.search,
                        self._get_detailed_instruct(task_description, query),
                        {"caseId": case_id},
                        5,
                    ): query
                    for query in queries
                }

                for future in as_completed(future_to_query):
                    query = future_to_query[future]
                    try:
                        retrieved_docs = future.result()
                        for doc in retrieved_docs:
                            if doc.id not in category_doc_ids:
                                category_doc_ids.add(doc.id)
                                category_documents.append(doc)
                    except Exception as e:
                        self.logger.warning(f"Search failed for query '{query}': {e}")

            category_contents[category] = category_documents
            self.logger.info(
                f"Category {category}: retrieved {len(category_documents)} unique documents"
            )
        return category_contents

    @timing_decorator
    def _review_retrieved_documents(self, category_contents: dict) -> dict:
        """审查从向量存储中检索到的一组唯一文档.

        1. 从所有类别中收集唯一的文档。
        2. 对每个唯一文档调用 `run_sync` 以获取审查结果（使用缓存）。
        3. 将所有单个文档的审查结果合并为最终的、去重的结果。

        Args:
            category_contents: 将类别映射到检索到的文档列表的字典。

        Returns:
            包含最终合并审查结果的字典。
        """
        # 1) 收集所有唯一的文档
        unique_docs = []
        seen_doc_ids = set()
        for docs in category_contents.values():
            for doc in docs or []:
                doc_id = getattr(doc, "id", None)
                if not doc_id:
                    # 对于没有ID的文档，使用内容哈希作为唯一标识
                    doc_id = hashlib.sha256(doc.content.encode("utf-8")).hexdigest()

                if doc_id not in seen_doc_ids:
                    seen_doc_ids.add(doc_id)
                    unique_docs.append(doc)

        self.logger.info(f"Total unique documents for review: {len(unique_docs)}")

        # 2) 对每个唯一文档审查一次（使用缓存）
        doc_results = []
        for i, doc in enumerate(unique_docs):
            try:
                # 使用与上面相同的逻辑生成缓存键
                cache_key = (
                    getattr(doc, "id", None)
                    or hashlib.sha256(doc.content.encode("utf-8")).hexdigest()
                )

                if cache_key in self._doc_review_cache:
                    doc_result = self._doc_review_cache[cache_key]
                else:
                    doc_result = self.run_sync(doc.content)
                    if isinstance(doc_result, dict):
                        self._doc_review_cache[cache_key] = doc_result

                if isinstance(doc_result, dict):
                    doc_results.append(doc_result)

            except Exception as e:
                self.logger.error(f"Failed to review unique document {i + 1}: {e}")
                continue

        # 3) 合并所有已审查文档的结果
        return self._merge_and_deduplicate_results(doc_results)

    def _add_summary_categories(self, results: dict) -> None:
        """将详细的类别结果汇总到 'sensitive_inquiry' 和 'sensitive_reply' 摘要类别中."""
        sensitive_inquiry_items = []
        sensitive_reply_items = []

        # 定义哪些类别属于 "inquiry"
        inquiry_categories = [
            "consulting_company_info",
            "selling_user_info",
            "negative_news",
            "major_complaints",
        ]
        # 定义哪些类别属于 "reply"
        reply_categories = ["request_contact_information", "spam_messages"]

        for category in inquiry_categories:
            if category in results and isinstance(results[category], list):
                sensitive_inquiry_items.extend(results[category])

        for category in reply_categories:
            if category in results and isinstance(results[category], list):
                sensitive_reply_items.extend(results[category])

        results["sensitive_inquiry"] = sensitive_inquiry_items
        results["sensitive_reply"] = sensitive_reply_items
        self.logger.info(
            f"Generated sensitive_inquiry with {len(sensitive_inquiry_items)} items"
        )
        self.logger.info(
            f"Generated sensitive_reply with {len(sensitive_reply_items)} items"
        )

    def _merge_and_deduplicate_results(self, list_of_results: list[dict]) -> dict:
        """将多个块/文档的结果合并为单一的、去重的结果."""
        if not list_of_results:
            return {}
        if len(list_of_results) == 1:
            return list_of_results[0]

        self.logger.info(
            f"Merging and deduplicating results from {len(list_of_results)} sources"
        )

        from collections import defaultdict

        collected_results = defaultdict(list)

        # 1. 收集所有结果
        for res in list_of_results:
            if isinstance(res, dict):
                for category, value in res.items():
                    collected_results[category].append(value)

        merged_results = {}

        # 2. 根据类别的数据类型进行处理
        for category, items in collected_results.items():
            if not items:
                continue

            first_item = items[0]

            if isinstance(
                first_item, list
            ):  # 例如: sensitive_inquiry, government_inquiry
                all_items = [
                    item
                    for sublist in items
                    if isinstance(sublist, list)
                    for item in sublist
                ]
                seen = set()
                deduplicated_items = []
                for item in all_items:
                    item_str = (
                        str(sorted(item.items()))
                        if isinstance(item, dict)
                        else str(item)
                    )
                    if item_str not in seen:
                        seen.add(item_str)
                        deduplicated_items.append(item)
                merged_results[category] = deduplicated_items

            elif (
                isinstance(first_item, dict) and "values" in first_item
            ):  # 例如: key_contact
                all_values = []
                hit_rule = False
                for item_dict in items:
                    if isinstance(item_dict, dict):
                        all_values.extend(item_dict.get("values", []))
                        if item_dict.get("hit_rule"):
                            hit_rule = True

                unique_values = list(dict.fromkeys(all_values))

                final_dict = first_item.copy()
                final_dict["values"] = unique_values
                final_dict["hit_rule"] = bool(unique_values) and hit_rule
                merged_results[category] = final_dict

            else:
                self.logger.warning(
                    f"Unknown result structure for category '{category}'. Taking first result."
                )
                merged_results[category] = first_item

        return merged_results

    @timing_decorator
    def _validate_and_sort_messages(self, messages, caseId):
        """Validate and sort messages by 'id' using Pydantic. Raises ValueError if invalid."""
        if not isinstance(messages, list):
            raise ValueError(
                f"caseId {caseId}: 'messages' must be a list, got {type(messages)}"
            )
        if not messages:
            self.logger.warning(f"caseId {caseId}: empty messages list")
            return []
        try:
            msgs = [MessageItem(**msg) for msg in messages]
        except ValidationError as e:
            raise ValueError(f"caseId {caseId}: invalid messages data - {e}")
        # sort by id and convert to dict
        return [m.model_dump() for m in sorted(msgs, key=lambda x: x.id)]

    def _process_values_and_matched_ids(self, item_with_values, messages):
        """Process a single item containing values, add matched_ids and deduplicate.

        Only keep values that can be found in the original messages.

        Args:
            item_with_values: Dictionary containing values field
            messages: Message list
        """
        # First deduplicate values while maintaining order
        unique_values = list(dict.fromkeys(item_with_values["values"]))

        # Only keep values that can be matched in original messages
        validated_values = []
        validated_matched_ids = []

        for value in unique_values:
            ids = [msg["id"] for msg in messages if value in msg["msg"]]
            if ids:  # Only keep values that have matches in original text
                validated_values.append(value)
                # Deduplicate ids for each value
                unique_ids = list(dict.fromkeys(ids))  # Order-preserving deduplication
                validated_matched_ids.append(unique_ids)
                self.logger.debug(
                    f"Validated value: '{value}' -> matched IDs: {unique_ids}"
                )
            else:
                self.logger.warning(
                    f"Discarded unverifiable value: '{value}' (not found in original messages)"
                )

        # Update with only validated values
        item_with_values["values"] = validated_values
        item_with_values["matched_ids"] = validated_matched_ids

        # Update hit_rule based on whether we have any validated values
        if not validated_values:
            item_with_values["hit_rule"] = False
            self.logger.info("No validated values found, setting hit_rule to False")

    def attach_matched_ids(self, review_res, messages):
        """Add matched_ids field to each review item with values, structured as a 2D array. Add matched_ids: [] even if values is empty.

        Deduplicate values and matched_ids to avoid duplicate content and IDs.
        """
        for _, items in review_res.items():
            if isinstance(items, list):
                for item in items:
                    if "values" in item:
                        self._process_values_and_matched_ids(item, messages)
            elif isinstance(items, dict) and "values" in items:
                self._process_values_and_matched_ids(items, messages)
        return review_res

    @timing_decorator
    def run(self, messages: list, caseId: str = ""):
        """Process and analyze messages, handling chunking and language detection."""
        self.logger.info(
            f"Processing caseId: {caseId} with {len(messages) if isinstance(messages, list) else 0} messages"
        )

        # Step 1: Validate and prepare messages
        try:
            msgs = self._validate_and_sort_messages(messages, caseId)
            if not msgs:
                return {"id": caseId, "review_res": {}}

            self.logger.info(
                f"Content prepared for caseId {caseId} with {len(msgs)} messages"
            )

        except ValueError as e:
            raise ValueError(f"Message validation failed: {e}")

        # Step 2: Handle content size and chunking using RetrievalCore
        try:
            # 使用RetrievalCore进行分块处理
            chunk_docs, chunk_count = self._split_messages_using_retrieval_core(
                msgs, caseId
            )

            self.logger.info(f"RetrievalCore generated {chunk_count} chunks")

            # 根据分块数量决定处理策略
            if chunk_count <= 10:
                self.logger.info(
                    f"Chunk count ({chunk_count}) <= 10, using normal sequential processing"
                )

                # 直接处理每个分块，不需要复杂的token计算
                results = []
                for i, doc in enumerate(chunk_docs):
                    self.logger.info(f"Processing chunk {i + 1}/{chunk_count}")

                    chunk_result = self.run_sync(doc.content)
                    results.append(chunk_result)

                    self.logger.info(f"Completed chunk {i + 1}/{chunk_count}")

                # 合并结果并返回
                merged_results = self._merge_and_deduplicate_results(results)
                merged_results = self.attach_matched_ids(merged_results, msgs)
                return {"id": caseId, "review_res": merged_results}

            else:
                self.logger.warning(
                    f"Chunk count ({chunk_count}) >= 10, using vector storage processing"
                )

                # 使用向量存储处理大量分块
                vector_result = self._process_large_chunk_count_with_vector_storage(
                    chunk_docs, caseId
                )

                # 直接返回向量处理结果，跳过后续的正常处理流程
                vector_result = self.attach_matched_ids(vector_result, msgs)
                return {"id": caseId, "review_res": vector_result}

        except Exception as e:
            self.logger.error(f"RetrievalCore processing failed: {e}")
            self.logger.error("No fallback available, RetrievalCore is required")
            raise ValueError(
                f"RetrievalCore processing failed and no fallback available: {e}"
            )

    def run_sync(self, content):
        """Run all review tasks sequentially and merge results."""
        result_dict = {}

        self.logger.info("Starting sequential execution of review tasks")

        # Execute tasks sequentially
        try:
            # Task 1: Key contact review
            self.logger.info("Executing task: key_contact")
            start_time = time.time()
            result_dict["key_contact"] = self.core_components.key_contact_review(
                content=content, risk_keywords=self.prompt_dict["key_contact"]
            )
            self.logger.info(
                f"Task key_contact completed in {time.time() - start_time:.2f}s"
            )

            # Task 2: Unified sensitive content review (main API call)
            self.logger.info("Executing task: unified_sensitive_review")
            start_time = time.time()
            unified_result = self.core_components.unified_all_review_sync(content)
            self.logger.info(
                f"Task unified_sensitive_review completed in {time.time() - start_time:.2f}s"
            )

            # Task 3: Government inquiry review
            self.logger.info("Executing task: government_inquiry")
            start_time = time.time()
            result_dict["government_inquiry"] = (
                self.core_components.government_inquiry_review(
                    content, self.prompt_dict["government_inquiry"]
                )
            )
            self.logger.info(
                f"Task government_inquiry completed in {time.time() - start_time:.2f}s"
            )

            # Task 4: Internal system review
            self.logger.info("Executing task: internal_system")
            start_time = time.time()
            result_dict["internal_system"] = self.core_components.key_contact_review(
                content=content,
                risk_keywords=self.prompt_dict["internal_system"],
            )
            self.logger.info(
                f"Task internal_system completed in {time.time() - start_time:.2f}s"
            )

        except Exception as e:
            self.logger.error(f"Error during sequential task execution: {e}")
            # Ensure we have some result structure even if tasks fail
            if "key_contact" not in result_dict:
                result_dict["key_contact"] = {}
            if "government_inquiry" not in result_dict:
                result_dict["government_inquiry"] = []
            if "internal_system" not in result_dict:
                result_dict["internal_system"] = {}
            unified_result = {"sensitive_inquiry": [], "sensitive_reply": []}

        self.logger.info("All review tasks completed sequentially")

        # Process unified review results and split back to original format
        if isinstance(unified_result, dict):
            # Extract sensitive_inquiry and sensitive_reply from unified result
            if "sensitive_inquiry" in unified_result:
                result_dict["sensitive_inquiry"] = unified_result["sensitive_inquiry"]
            else:
                result_dict["sensitive_inquiry"] = []

            if "sensitive_reply" in unified_result:
                result_dict["sensitive_reply"] = unified_result["sensitive_reply"]
            else:
                result_dict["sensitive_reply"] = []
        else:
            # Fallback if unified result format is unexpected
            self.logger.warning("Unified result format unexpected, using empty results")
            result_dict["sensitive_inquiry"] = []
            result_dict["sensitive_reply"] = []

        return result_dict


if __name__ == "__main__":
    # Use context manager to ensure proper resource cleanup
    # with BasicPipeline() as pipeline:
    pipeline = BasicPipeline()
    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    from pprint import pprint

    pprint(pipeline.run(messages=messages, caseId=caseId))
