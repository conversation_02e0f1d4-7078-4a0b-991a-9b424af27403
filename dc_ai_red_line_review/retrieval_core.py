import hashlib
import os
import warnings

import urllib3
from chonkie import TokenChunker
from dotenv import load_dotenv
from haystack import Document
from haystack.components.embedders import OpenAIDocumentEmbedder, OpenAITextEmbedder
from haystack.components.preprocessors import DocumentSplitter
from haystack.document_stores.types import DuplicatePolicy
from haystack.utils import Secret
from haystack_integrations.components.retrievers.opensearch import (
    OpenSearchHybridRetriever,
)
from haystack_integrations.document_stores.opensearch import OpenSearchDocumentStore

from dc_ai_red_line_review.utils import timing_decorator

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings("ignore", message=".*verify_certs=False.*")

load_dotenv(override=True)


class RetrievalCore:
    def __init__(
        self,
        split_length: int = 8000,
        split_overlap: int = 1000,
    ):
        self.doc_store = OpenSearchDocumentStore(
            hosts=f"https://{os.environ['OPENSEARCH_HOST']}",
            http_auth=(
                os.environ["OPENSEARCH_USERNAME"],
                os.environ["OPENSEARCH_PASSWORD"],
            ),
            index=os.environ["OPENSEARCH_INDEX"],
            embedding_dim=int(os.environ["EMBEDDING_DIMS"]),
            verify_certs=False,
            use_ssl=True,
        )

        self.embed_config = {
            "api_base_url": os.environ["EMBEDDING_BASE_URL"],
            "api_key": Secret.from_token(os.environ["EMBEDDING_API_KEY"]),
            "model": os.environ["EMBEDDING_MODEL"],
            "http_client_kwargs": {"verify": False, "timeout": 30.0},
        }

        self.doc_embedder = OpenAIDocumentEmbedder(**self.embed_config)
        self.text_embedder = OpenAITextEmbedder(**self.embed_config)

        self.retriever = OpenSearchHybridRetriever(
            document_store=self.doc_store,
            embedder=self.text_embedder,
            top_k_bm25=5,  # Increased from 3 to capture more potential matches
            top_k_embedding=5,  # Increased from 3 to capture more potential matches
            join_mode="reciprocal_rank_fusion",
        )

        self.chunker = TokenChunker(
            tokenizer="character",  # Default tokenizer (or use "gpt2", etc.)
            chunk_size=split_length,  # Maximum tokens per chunk
            chunk_overlap=split_overlap,  # Overlap between chunks
        )

    def _convert_filters_to_haystack_format(self, filters: dict = None) -> dict:
        """Convert simple filters format to Haystack v2.0 format.

        Args:
            filters: Simple filters like {"caseId": "123"} or None

        Returns:
            Haystack v2.0 format filters or None
        """
        if not filters:
            return None

        # If already in Haystack v2.0 format (has 'operator' key), return as is
        if isinstance(filters, dict) and ("operator" in filters or "field" in filters):
            return filters

        # Convert simple format to Haystack v2.0 format
        conditions = []
        for field, value in filters.items():
            conditions.append({"field": field, "operator": "==", "value": value})

        if len(conditions) == 1:
            return conditions[0]
        else:
            return {"operator": "AND", "conditions": conditions}

    @timing_decorator
    def _get_splitter_res(
        self, case_id, messages, split_length: int = None, split_overlap: int = None
    ):
        formatted_parts = [
            f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>" for msg in messages
        ]

        doc = Document(content="\n".join(formatted_parts), meta={"caseId": case_id})

        # Use a temporary chunker with dynamic parameters to avoid rebuilding core components
        effective_length = (
            split_length if split_length is not None else self.chunker.chunk_size
        )
        effective_overlap = (
            split_overlap if split_overlap is not None else self.chunker.chunk_overlap
        )
        temp_chunker = TokenChunker(
            tokenizer="character",
            chunk_size=effective_length,
            chunk_overlap=effective_overlap,
        )

        # Create a wrapper function that converts chonkie chunks to strings
        def chunk_to_strings(text):
            chunks = temp_chunker.chunk(text)
            # Convert chunk objects to strings
            return [str(chunk) for chunk in chunks]

        splitter = DocumentSplitter(
            split_by="function",
            splitting_function=chunk_to_strings,
        )

        docs = splitter.run(documents=[doc])["documents"]
        # Assign deterministic IDs to chunks to enable deduplication and controlled lifecycle
        for d in docs:
            try:
                d.id = hashlib.sha256(d.content.encode("utf-8")).hexdigest()
            except Exception:
                pass
        return docs

    @timing_decorator
    def insert_documents(self, chunk_docs):
        docs = self.doc_embedder.run(documents=chunk_docs)
        self.doc_store.write_documents(docs["documents"], policy=DuplicatePolicy.SKIP)

    @timing_decorator
    def search(self, query: str, filters: dict = None, top_k: int = None):
        """Search documents with query and metadata filters.

        Args:
            query: Search query string
            filters: Dictionary of metadata filters, e.g. {"caseId": "123"}
            top_k: Number of documents to return (overrides default if provided)
        """
        # Convert filters to Haystack v2.0 format
        haystack_filters = self._convert_filters_to_haystack_format(filters)

        # Use dynamic top_k if provided, otherwise use default retriever settings
        if top_k and (top_k != getattr(self, "_default_top_k", None)):
            # lazily cache a custom retriever per top_k to avoid rebuilding every query
            if not hasattr(self, "_custom_retrievers"):
                self._custom_retrievers = {}
            if top_k not in self._custom_retrievers:
                custom_text_embedder = OpenAITextEmbedder(**self.embed_config)
                self._custom_retrievers[top_k] = OpenSearchHybridRetriever(
                    document_store=self.doc_store,
                    embedder=custom_text_embedder,
                    top_k_bm25=top_k,
                    top_k_embedding=top_k,
                    join_mode="reciprocal_rank_fusion",
                )
            retriever = self._custom_retrievers[top_k]
            results = retriever.run(
                query=query,
                filters_bm25=haystack_filters,
                filters_embedding=haystack_filters,
            )
        else:
            results = self.retriever.run(
                query=query,
                filters_bm25=haystack_filters,
                filters_embedding=haystack_filters,
            )

        documents = results["documents"]
        return documents

    def delete_all_documents(self):
        documents = self.doc_store.filter_documents()
        for doc in documents:
            self.doc_store.delete_documents([doc.id])

    def delete_documents_by_case(self, case_id: str):
        """Delete documents belonging to a specific caseId."""
        docs = self.doc_store.filter_documents(
            filters={"field": "caseId", "operator": "==", "value": case_id}
        )
        for d in docs:
            self.doc_store.delete_documents([d.id])


if __name__ == "__main__":
    caseId = "10066130"
    messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "customer",
        },
        {
            "id": 2,
            "type": "USER",
            "msg": "customer support",
        },
        {
            "id": 3,
            "type": "USER",
            "msg": "support",
        },
        {
            "id": 4,
            "type": "USER",
            "msg": "KuCoin Pay Menu",
        },
        {
            "id": 5,
            "type": "AGENT",
            "msg": "Hi, it's Jacob here, thank you for contacting our customer support. I understand your frustration and rest assured that I will help you through out this process. I would appreciate if you could give me 2-3 minutes to understand your issue and address the best possible solution as soon as possible. Thank you for your patience.",
        },
        {
            "id": 6,
            "type": "AGENT",
            "msg": "I apologize for the inconvenience, Please hold on the line while I transfer you to our relevant representatives for further assistance.",
        },
        {
            "id": 7,
            "type": "USER",
            "msg": "Hi 我要報稅 機關那邊要我提供交易所主體所在地",
        },
        {
            "id": 8,
            "type": "AGENT",
            "msg": "您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。",
        },
        {
            "id": 9,
            "type": "USER",
            "msg": "好的",
        },
        {
            "id": 10,
            "type": "AGENT",
            "msg": "您好，請問您是想咨詢KuCoin的注冊地址嗎？",
        },
        {
            "id": 11,
            "type": "USER",
            "msg": "是的",
        },
        {
            "id": 12,
            "type": "USER",
            "msg": "報稅機關要求我提供",
        },
        {
            "id": 13,
            "type": "AGENT",
            "msg": "好的請稍等",
        },
        {
            "id": 14,
            "type": "AGENT",
            "msg": "KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mah&eacute; , Republic of Seychelles",
        },
        {
            "id": 15,
            "type": "USER",
            "msg": "謝謝你",
        },
        {
            "id": 16,
            "type": "AGENT",
            "msg": "不客气",
        },
        {
            "id": 17,
            "type": "AGENT",
            "msg": "請問還有其他可以幫助您的嗎？",
        },
        {
            "id": 18,
            "type": "AGENT",
            "msg": "我發現你已經有一段時間沒有回應了，我將暫時結束此對話，如果您有任何其他問題，請隨時與我們聯繫。我們提供7*24小時客戶支援！😊",
        },
    ]

    knowledge_base = RetrievalCore()
    chunk_docs = knowledge_base._get_splitter_res(case_id=caseId, messages=messages)
    print(len(chunk_docs))

    knowledge_base.insert_documents(chunk_docs)
    print(knowledge_base.search("公司的地址在哪里?"))

    print(knowledge_base.doc_store.count_documents())
    knowledge_base.delete_all_documents()
    print(knowledge_base.doc_store.count_documents())
